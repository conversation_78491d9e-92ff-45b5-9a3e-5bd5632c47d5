@echo off
echo [*] Checking Python Installation...

where python >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    echo [!] Python not found. Please install Python from https://www.python.org/downloads/
    pause
    exit /b
)

echo [*] Python is installed.
echo [*] Setting temporary PATH...

REM استخراج مجلد التثبيت الحالي
FOR /F "delims=" %%I IN ('python -c "import sys; print(sys.executable)"') DO set PYTHONPATH=%%~dpI
set PATH=%PYTHONPATH%;%PATH%

echo [*] Updated PATH: %PYTHONPATH%
echo [*] Checking pip...

python -m ensurepip --upgrade
python -m pip install --upgrade pip

echo [*] Installing requirements.txt...

IF EXIST requirements.txt (
    python -m pip install -r requirements.txt
) ELSE (
    echo [!] requirements.txt not found in current directory.
)

pause
