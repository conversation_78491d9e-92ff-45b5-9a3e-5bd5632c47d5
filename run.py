#!/usr/bin/env python3
"""
ملف تشغيل مبسط للنظام
Simple runner for the crypto prediction system
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات"""
    required_packages = [
        'flask',
        'flask-cors', 
        'numpy',
        'pandas'
    ]
    
    print("📦 جاري تثبيت المكتبات المطلوبة...")
    print("📦 Installing required packages...")
    
    for package in required_packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                stdout=subprocess.DEVNULL, 
                                stderr=subprocess.DEVNULL)
            print(f"✅ {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️ {package} - قد يكون مثبت مسبقاً")

def main():
    """الدالة الرئيسية"""
    print("🚀 نظام التنبؤ بالعملات المشفرة")
    print("🚀 Advanced Crypto Prediction System")
    print("=" * 50)
    
    # تثبيت المتطلبات
    install_requirements()
    
    print("\n🌐 جاري تشغيل الخادم...")
    print("🌐 Starting server...")
    print("\n🔗 الروابط المتاحة:")
    print("🔗 Available URLs:")
    print("   http://localhost:5000")
    print("   http://127.0.0.1:5000")
    print("\n⚠️ ملاحظة: استخدم http:// وليس https://")
    print("⚠️ Note: Use http:// not https://")
    print("\n⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("⏹️ Press Ctrl+C to stop server")
    print("=" * 50)
    
    # تشغيل الخادم
    try:
        if os.path.exists('simple_server.py'):
            subprocess.run([sys.executable, 'simple_server.py'])
        else:
            print("❌ ملف simple_server.py غير موجود!")
            print("❌ simple_server.py file not found!")
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف الخادم بنجاح!")
        print("✅ Server stopped successfully!")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    main()
