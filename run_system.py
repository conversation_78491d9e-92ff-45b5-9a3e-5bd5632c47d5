#!/usr/bin/env python3
"""
ملف تشغيل النظام المتقدم للتنبؤ بأسعار العملات المشفرة
System Launcher for Advanced Cryptocurrency Price Forecasting System
"""

import os
import sys
import time
import argparse
import subprocess
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🚀 النظام المتقدم للتنبؤ بأسعار العملات المشفرة 🚀        ║
    ║         Advanced Cryptocurrency Price Forecasting            ║
    ║                                                              ║
    ║    🧠 LSTM + Ensemble Models + Sentiment Analysis 🧠         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 9):
        print("❌ خطأ: يتطلب Python 3.9 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        sys.exit(1)
    else:
        print(f"✅ Python {sys.version.split()[0]} - متوافق")

def check_dependencies():
    """التحقق من المتطلبات"""
    print("\n🔍 التحقق من المتطلبات...")
    
    required_packages = [
        'pandas', 'numpy', 'tensorflow', 'sklearn', 'flask',
        'yfinance', 'ta', 'transformers', 'xgboost'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - مفقود")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  المتطلبات المفقودة: {', '.join(missing_packages)}")
        print("تشغيل: pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def setup_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📁 إنشاء المجلدات...")
    
    directories = ['data', 'logs', 'saved_models', 'static/css', 'static/js', 'templates']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ {directory}")

def install_dependencies():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def collect_initial_data():
    """جمع البيانات الأولية"""
    print("\n📊 جمع البيانات الأولية...")
    
    try:
        from data_collector import CryptoDataCollector
        from config import Config
        
        collector = CryptoDataCollector()
        
        for symbol in Config.CRYPTO_SYMBOLS:
            print(f"📈 جمع بيانات {symbol}...")
            data = collector.get_historical_data(symbol)
            
            if not data.empty:
                collector.save_data(data, symbol)
                print(f"✅ تم حفظ {len(data)} سجل لـ {symbol}")
            else:
                print(f"⚠️  لا توجد بيانات لـ {symbol}")
        
        print("✅ تم جمع البيانات الأولية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في جمع البيانات: {e}")
        return False

def run_tests():
    """تشغيل الاختبارات"""
    print("\n🧪 تشغيل الاختبارات...")
    
    try:
        # اختبار استيراد الوحدات
        from data_collector import CryptoDataCollector
        from feature_engineering import FeatureEngineer
        from models.lstm_model import LSTMPredictor
        from models.ensemble_model import EnsemblePredictor
        
        print("✅ جميع الوحدات تعمل بشكل صحيح")
        
        # اختبار جمع البيانات
        collector = CryptoDataCollector()
        test_data = collector.get_historical_data('BTC-USD')
        
        if not test_data.empty:
            print("✅ جمع البيانات يعمل")
        else:
            print("⚠️  مشكلة في جمع البيانات")
        
        # اختبار هندسة الخصائص
        engineer = FeatureEngineer()
        featured_data = engineer.create_features(test_data.head(100))
        
        if len(featured_data.columns) > len(test_data.columns):
            print("✅ هندسة الخصائص تعمل")
        else:
            print("⚠️  مشكلة في هندسة الخصائص")
        
        print("✅ جميع الاختبارات نجحت")
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاختبارات: {e}")
        return False

def start_web_server():
    """تشغيل خادم الويب"""
    print("\n🌐 تشغيل خادم الويب...")

    try:
        from app import app
        from config import Config

        print(f"🚀 النظام يعمل على: http://{Config.HOST}:{Config.PORT}")
        print("\n📋 الصفحات المتاحة:")
        print(f"   🏠 لوحة التحكم: http://{Config.HOST}:{Config.PORT}/")
        print(f"   🔮 التنبؤات: http://{Config.HOST}:{Config.PORT}/predictions")
        print(f"   📊 التحليل: http://{Config.HOST}:{Config.PORT}/analysis")
        print(f"   🤖 النماذج: http://{Config.HOST}:{Config.PORT}/models")
        print("\n📡 واجهات برمجة التطبيقات:")
        print(f"   📈 البيانات: http://{Config.HOST}:{Config.PORT}/api/data/BTC-USD")
        print(f"   🎯 التنبؤات: http://{Config.HOST}:{Config.PORT}/api/predict/BTC-USD")
        print(f"   📊 التحليل: http://{Config.HOST}:{Config.PORT}/api/analysis/BTC-USD")
        print(f"   ⚙️  الحالة: http://{Config.HOST}:{Config.PORT}/api/status")
        print("\n📱 افتح المتصفح وانتقل إلى أي من الروابط أعلاه")
        print("⏹️  اضغط Ctrl+C لإيقاف النظام")

        app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG)

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description='تشغيل نظام التنبؤ بأسعار العملات المشفرة')
    parser.add_argument('--install', action='store_true', help='تثبيت المتطلبات')
    parser.add_argument('--collect-data', action='store_true', help='جمع البيانات الأولية')
    parser.add_argument('--test', action='store_true', help='تشغيل الاختبارات')
    parser.add_argument('--no-banner', action='store_true', help='إخفاء الشعار')
    
    args = parser.parse_args()
    
    if not args.no_banner:
        print_banner()
    
    print(f"🕐 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # التحقق من إصدار Python
    check_python_version()
    
    # إنشاء المجلدات
    setup_directories()
    
    # تثبيت المتطلبات إذا طُلب ذلك
    if args.install:
        if not install_dependencies():
            sys.exit(1)
    
    # التحقق من المتطلبات
    if not check_dependencies():
        print("\n💡 تشغيل: python run_system.py --install")
        sys.exit(1)
    
    # جمع البيانات الأولية إذا طُلب ذلك
    if args.collect_data:
        if not collect_initial_data():
            print("⚠️  تحذير: فشل في جمع بعض البيانات")
    
    # تشغيل الاختبارات إذا طُلب ذلك
    if args.test:
        if not run_tests():
            sys.exit(1)
        return
    
    # تشغيل النظام
    print("\n🎯 جاهز للتشغيل!")
    time.sleep(2)
    start_web_server()

if __name__ == "__main__":
    main()
