
from flask import Flask, render_template, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
from datetime import datetime
import os

app = Flask(__name__)
CORS(app)

# Mock data for testing
CRYPTO_SYMBOLS = ['BTC-USD', 'ETH-USD', 'ADA-USD']

@app.route('/')
def index():
    return render_template('dashboard.html')

@app.route('/predictions')
def predictions():
    return render_template('predictions.html')

@app.route('/analysis')
def analysis():
    return render_template('analysis.html')

@app.route('/models')
def models_page():
    return render_template('models.html')

@app.route('/api/symbols')
def get_symbols():
    return jsonify({
        'symbols': CRYPTO_SYMBOLS,
        'default': 'BTC-USD'
    })

@app.route('/api/status')
def get_status():
    return jsonify({
        'system_status': 'نشط',
        'models_initialized': len(CRYPTO_SYMBOLS),
        'last_update': datetime.now().isoformat()
    })

@app.route('/api/data/<symbol>')
def get_data(symbol):
    # Mock historical data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    prices = 50000 + np.cumsum(np.random.randn(100) * 100)
    
    historical_data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        historical_data.append({
            'date': date.isoformat(),
            'open': price + np.random.randn() * 50,
            'high': price + abs(np.random.randn() * 100),
            'low': price - abs(np.random.randn() * 100),
            'close': price,
            'volume': np.random.randint(1000000, 10000000)
        })
    
    return jsonify({
        'symbol': symbol,
        'historical_data': historical_data[-30:],  # Last 30 days
        'real_time_data': {
            'current_price': float(prices[-1]),
            'previous_close': float(prices[-2]),
            'day_high': float(prices[-1] + 500),
            'day_low': float(prices[-1] - 500),
            'volume': int(np.random.randint(5000000, 15000000))
        },
        'sentiment_score': np.random.uniform(-0.5, 0.5),
        'last_updated': datetime.now().isoformat()
    })

@app.route('/api/predict/<symbol>')
def get_predictions(symbol):
    current_price = 50000 + np.random.randn() * 5000
    
    predictions = {
        'symbol': symbol,
        'current_price': current_price,
        'formatted_current_price': f"${current_price:,.2f}",
        'predictions': {
            'lstm': {},
            'ensemble': {}
        },
        'timestamp': datetime.now().isoformat()
    }
    
    # Mock predictions
    for horizon in [1, 3, 7, 30]:
        change = np.random.uniform(-0.05, 0.05)
        pred_price = current_price * (1 + change)
        
        predictions['predictions']['lstm'][f'{horizon}d'] = {
            'predicted_price': pred_price,
            'formatted_price': f"${pred_price:,.2f}",
            'change_percentage': change * 100,
            'trend': 'صاعد' if change > 0 else 'هابط',
            'emoji': '📈' if change > 0 else '📉'
        }
    
    return jsonify(predictions)

if __name__ == '__main__':
    print("🌐 تشغيل الخادم...")
    print("🔗 الروابط المتاحة:")
    print("   http://localhost:5000/ - الصفحة الرئيسية")
    print("   http://localhost:5000/predictions - التنبؤات")
    print("   http://localhost:5000/analysis - التحليل")
    print("   http://localhost:5000/models - النماذج")
    print("\n⏹️ اضغط Ctrl+C لإيقاف الخادم")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
