"""
تطبيق الويب للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Web Application for Advanced Cryptocurrency Price Forecasting System
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import threading
import time

from config import Config
from data_collector import CryptoDataCollector
from feature_engineering import FeatureEngineer
from models.lstm_model import LSTMPredictor
from models.ensemble_model import EnsemblePredictor
from utils import logger, format_currency, calculate_percentage_change, get_trend_direction

# إنشاء التطبيق
app = Flask(__name__)
CORS(app)
app.config['SECRET_KEY'] = Config.SECRET_KEY

# المتغيرات العامة
data_collector = CryptoDataCollector()
feature_engineer = FeatureEngineer()
models = {}
latest_data = {}
latest_predictions = {}

def initialize_models():
    """تهيئة النماذج"""
    global models
    try:
        logger.info("تهيئة النماذج...")
        
        for symbol in Config.CRYPTO_SYMBOLS:
            models[symbol] = {
                'lstm': LSTMPredictor(),
                'ensemble': EnsemblePredictor()
            }
        
        logger.info("تم تهيئة النماذج بنجاح")
        
    except Exception as e:
        logger.error(f"خطأ في تهيئة النماذج: {e}")

def update_data_and_predictions():
    """تحديث البيانات والتنبؤات"""
    global latest_data, latest_predictions
    
    while True:
        try:
            logger.info("تحديث البيانات والتنبؤات...")
            
            for symbol in Config.CRYPTO_SYMBOLS:
                # جمع البيانات الحديثة
                historical_data = data_collector.get_historical_data(symbol)
                real_time_data = data_collector.get_real_time_data(symbol)
                sentiment_score = data_collector.get_sentiment_score(symbol)
                
                if not historical_data.empty:
                    # هندسة الخصائص
                    featured_data = feature_engineer.create_features(
                        historical_data, 
                        sentiment_scores=sentiment_score
                    )
                    
                    # حفظ البيانات الحديثة
                    latest_data[symbol] = {
                        'historical': featured_data,
                        'real_time': real_time_data,
                        'sentiment': sentiment_score,
                        'last_updated': datetime.now()
                    }
                    
                    # إنشاء التنبؤات (إذا كان النموذج مدرب)
                    if symbol in models and models[symbol]['lstm'].model is not None:
                        predictions = generate_predictions(symbol, featured_data)
                        latest_predictions[symbol] = predictions
            
            logger.info("تم تحديث البيانات والتنبؤات")
            
        except Exception as e:
            logger.error(f"خطأ في تحديث البيانات: {e}")
        
        # انتظار قبل التحديث التالي
        time.sleep(Config.UPDATE_INTERVAL_MINUTES * 60)

def generate_predictions(symbol, data):
    """إنشاء التنبؤات لعملة معينة"""
    try:
        if symbol not in models:
            return None
        
        # الحصول على آخر تسلسل للتنبؤ
        sequence_length = Config.SEQUENCE_LENGTH
        if len(data) < sequence_length:
            return None
        
        last_sequence = data.iloc[-sequence_length:].values
        current_price = data['close'].iloc[-1]
        
        predictions = {}
        
        # تنبؤات LSTM
        lstm_model = models[symbol]['lstm']
        if lstm_model.model is not None:
            lstm_predictions = []
            for horizon in Config.PREDICTION_HORIZONS:
                pred = lstm_model.predict_future(last_sequence, steps=horizon)
                if pred is not None and len(pred) > 0:
                    lstm_predictions.append(pred[-1])
                else:
                    lstm_predictions.append(current_price)
            
            predictions['lstm'] = dict(zip(Config.PREDICTION_HORIZONS, lstm_predictions))
        
        # تنبؤات Ensemble (إذا كان متاحاً)
        ensemble_model = models[symbol]['ensemble']
        if ensemble_model.is_trained:
            ensemble_pred = ensemble_model.predict(data.tail(100))  # آخر 100 نقطة
            if ensemble_pred is not None and len(ensemble_pred) > 0:
                predictions['ensemble'] = {
                    1: ensemble_pred[-1],
                    3: ensemble_pred[-1] * 1.01,  # تقدير بسيط
                    7: ensemble_pred[-1] * 1.02,
                    30: ensemble_pred[-1] * 1.05
                }
        
        # إضافة معلومات إضافية
        predictions['current_price'] = current_price
        predictions['timestamp'] = datetime.now().isoformat()
        predictions['symbol'] = symbol
        
        return predictions
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء التنبؤات لـ {symbol}: {e}")
        return None

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('dashboard.html')

@app.route('/api/symbols')
def get_symbols():
    """الحصول على قائمة العملات المدعومة"""
    return jsonify({
        'symbols': Config.CRYPTO_SYMBOLS,
        'default': Config.DEFAULT_SYMBOL
    })

@app.route('/api/data/<symbol>')
def get_data(symbol):
    """الحصول على بيانات عملة معينة"""
    try:
        if symbol not in Config.CRYPTO_SYMBOLS:
            return jsonify({'error': 'عملة غير مدعومة'}), 400
        
        if symbol in latest_data:
            data = latest_data[symbol]
            
            # تحويل البيانات التاريخية إلى JSON
            historical_json = data['historical'].tail(100).to_dict('records')
            
            return jsonify({
                'symbol': symbol,
                'historical_data': historical_json,
                'real_time_data': data['real_time'],
                'sentiment_score': data['sentiment'],
                'last_updated': data['last_updated'].isoformat()
            })
        else:
            return jsonify({'error': 'البيانات غير متاحة'}), 404
            
    except Exception as e:
        logger.error(f"خطأ في الحصول على البيانات: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

@app.route('/api/predict/<symbol>')
def get_predictions(symbol):
    """الحصول على التنبؤات لعملة معينة"""
    try:
        if symbol not in Config.CRYPTO_SYMBOLS:
            return jsonify({'error': 'عملة غير مدعومة'}), 400
        
        if symbol in latest_predictions and latest_predictions[symbol]:
            predictions = latest_predictions[symbol]
            
            # إضافة تحليل الاتجاه
            current_price = predictions['current_price']
            
            analysis = {}
            for model_type in ['lstm', 'ensemble']:
                if model_type in predictions:
                    model_predictions = predictions[model_type]
                    analysis[model_type] = {}
                    
                    for horizon, pred_price in model_predictions.items():
                        trend, emoji = get_trend_direction(current_price, pred_price)
                        change_pct = calculate_percentage_change(current_price, pred_price)
                        
                        analysis[model_type][f'{horizon}d'] = {
                            'predicted_price': pred_price,
                            'formatted_price': format_currency(pred_price),
                            'change_percentage': change_pct,
                            'trend': trend,
                            'emoji': emoji
                        }
            
            return jsonify({
                'symbol': symbol,
                'current_price': current_price,
                'formatted_current_price': format_currency(current_price),
                'predictions': analysis,
                'timestamp': predictions['timestamp']
            })
        else:
            return jsonify({'error': 'التنبؤات غير متاحة'}), 404
            
    except Exception as e:
        logger.error(f"خطأ في الحصول على التنبؤات: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

@app.route('/api/train/<symbol>', methods=['POST'])
def train_model(symbol):
    """تدريب النموذج لعملة معينة"""
    try:
        if symbol not in Config.CRYPTO_SYMBOLS:
            return jsonify({'error': 'عملة غير مدعومة'}), 400
        
        # جمع البيانات
        logger.info(f"بدء تدريب النموذج لـ {symbol}")
        
        historical_data = data_collector.get_historical_data(symbol)
        if historical_data.empty:
            return jsonify({'error': 'لا توجد بيانات كافية'}), 400
        
        # هندسة الخصائص
        sentiment_score = data_collector.get_sentiment_score(symbol)
        featured_data = feature_engineer.create_features(historical_data, sentiment_score)
        
        if len(featured_data) < Config.SEQUENCE_LENGTH + 100:
            return jsonify({'error': 'البيانات غير كافية للتدريب'}), 400
        
        # تدريب النموذج
        lstm_model = models[symbol]['lstm']
        lstm_model.n_features = len(featured_data.columns)
        lstm_model.build_model('standard')
        
        # تحضير البيانات
        X, y = lstm_model.prepare_data(featured_data)
        
        if X is None or len(X) == 0:
            return jsonify({'error': 'فشل في تحضير البيانات'}), 400
        
        # تقسيم البيانات
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        # التدريب
        history = lstm_model.train(X_train, y_train, X_val, y_val)
        
        if history is not None:
            # حفظ النموذج
            model_path = Config.get_model_path(symbol, 'lstm')
            lstm_model.save_model(model_path)
            
            # تقييم النموذج
            metrics = lstm_model.evaluate(X_val, y_val)
            
            logger.info(f"تم تدريب النموذج بنجاح لـ {symbol}")
            
            return jsonify({
                'message': f'تم تدريب النموذج بنجاح لـ {symbol}',
                'metrics': metrics,
                'training_samples': len(X_train),
                'validation_samples': len(X_val)
            })
        else:
            return jsonify({'error': 'فشل في التدريب'}), 500
            
    except Exception as e:
        logger.error(f"خطأ في تدريب النموذج: {e}")
        return jsonify({'error': f'خطأ في التدريب: {str(e)}'}), 500

@app.route('/api/status')
def get_status():
    """الحصول على حالة النظام"""
    try:
        status = {
            'system_status': 'نشط',
            'models_initialized': len(models),
            'supported_symbols': Config.CRYPTO_SYMBOLS,
            'last_update': datetime.now().isoformat(),
            'data_status': {},
            'model_status': {}
        }
        
        # حالة البيانات
        for symbol in Config.CRYPTO_SYMBOLS:
            if symbol in latest_data:
                status['data_status'][symbol] = {
                    'available': True,
                    'last_updated': latest_data[symbol]['last_updated'].isoformat(),
                    'records_count': len(latest_data[symbol]['historical'])
                }
            else:
                status['data_status'][symbol] = {'available': False}
        
        # حالة النماذج
        for symbol in Config.CRYPTO_SYMBOLS:
            if symbol in models:
                lstm_trained = models[symbol]['lstm'].model is not None
                ensemble_trained = models[symbol]['ensemble'].is_trained
                
                status['model_status'][symbol] = {
                    'lstm_trained': lstm_trained,
                    'ensemble_trained': ensemble_trained
                }
            else:
                status['model_status'][symbol] = {
                    'lstm_trained': False,
                    'ensemble_trained': False
                }
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على الحالة: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

if __name__ == '__main__':
    # تهيئة النماذج
    initialize_models()
    
    # بدء خيط تحديث البيانات
    update_thread = threading.Thread(target=update_data_and_predictions, daemon=True)
    update_thread.start()
    
    # تشغيل التطبيق
    logger.info(f"تشغيل التطبيق على {Config.HOST}:{Config.PORT}")
    app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG)
