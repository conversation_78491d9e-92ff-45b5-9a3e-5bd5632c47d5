"""
تطبيق الويب للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Web Application for Advanced Cryptocurrency Price Forecasting System
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import threading
import time

from config import Config
from data_collector import CryptoDataCollector
from feature_engineering import FeatureEngineer
from models.lstm_model import LSTMPredictor
from models.ensemble_model import EnsemblePredictor
from utils import logger, format_currency, calculate_percentage_change, get_trend_direction

# إنشاء التطبيق
app = Flask(__name__)
CORS(app)
app.config['SECRET_KEY'] = Config.SECRET_KEY

# المتغيرات العامة
data_collector = CryptoDataCollector()
feature_engineer = FeatureEngineer()
models = {}
latest_data = {}
latest_predictions = {}

def initialize_models():
    """تهيئة النماذج"""
    global models
    try:
        logger.info("تهيئة النماذج...")
        
        for symbol in Config.CRYPTO_SYMBOLS:
            models[symbol] = {
                'lstm': LSTMPredictor(),
                'ensemble': EnsemblePredictor()
            }
        
        logger.info("تم تهيئة النماذج بنجاح")
        
    except Exception as e:
        logger.error(f"خطأ في تهيئة النماذج: {e}")

def update_data_and_predictions():
    """تحديث البيانات والتنبؤات"""
    global latest_data, latest_predictions

    while True:
        try:
            logger.info("تحديث البيانات والتنبؤات...")

            for symbol in Config.CRYPTO_SYMBOLS:
                try:
                    # جمع البيانات الحديثة
                    historical_data = data_collector.get_historical_data(symbol)
                    real_time_data = data_collector.get_real_time_data(symbol)
                    sentiment_score = data_collector.get_sentiment_score(symbol)

                    if not historical_data.empty:
                        # هندسة الخصائص
                        featured_data = feature_engineer.create_features(
                            historical_data,
                            sentiment_scores=sentiment_score
                        )

                        # حفظ البيانات الحديثة
                        latest_data[symbol] = {
                            'historical': featured_data,
                            'real_time': real_time_data,
                            'sentiment': sentiment_score,
                            'last_updated': datetime.now()
                        }

                        # إنشاء التنبؤات (إذا كان النموذج مدرب)
                        if symbol in models:
                            predictions = generate_predictions(symbol, featured_data)
                            if predictions:
                                latest_predictions[symbol] = predictions

                        logger.info(f"تم تحديث البيانات لـ {symbol}")

                except Exception as e:
                    logger.error(f"خطأ في تحديث البيانات لـ {symbol}: {e}")
                    continue

            logger.info("تم الانتهاء من دورة التحديث")

        except Exception as e:
            logger.error(f"خطأ عام في تحديث البيانات: {e}")

        # انتظار قبل التحديث التالي
        time.sleep(Config.UPDATE_INTERVAL_MINUTES * 60)

def generate_predictions(symbol, data):
    """إنشاء التنبؤات لعملة معينة"""
    try:
        if symbol not in models:
            logger.warning(f"النموذج غير متوفر لـ {symbol}")
            return None

        # الحصول على آخر تسلسل للتنبؤ
        sequence_length = Config.SEQUENCE_LENGTH
        if len(data) < sequence_length:
            logger.warning(f"البيانات غير كافية لـ {symbol}: {len(data)} < {sequence_length}")
            return None

        last_sequence = data.iloc[-sequence_length:].values
        current_price = data['close'].iloc[-1]

        predictions = {}

        # تنبؤات LSTM
        lstm_model = models[symbol]['lstm']
        if lstm_model.model is not None:
            try:
                lstm_predictions = []
                for horizon in Config.PREDICTION_HORIZONS:
                    pred = lstm_model.predict_future(last_sequence, steps=horizon)
                    if pred is not None and len(pred) > 0:
                        lstm_predictions.append(float(pred[-1]))
                    else:
                        lstm_predictions.append(float(current_price))

                predictions['lstm'] = dict(zip(Config.PREDICTION_HORIZONS, lstm_predictions))
                logger.info(f"تم إنشاء تنبؤات LSTM لـ {symbol}")

            except Exception as e:
                logger.error(f"خطأ في تنبؤات LSTM لـ {symbol}: {e}")

        # تنبؤات Ensemble (إذا كان متاحاً)
        ensemble_model = models[symbol]['ensemble']
        if hasattr(ensemble_model, 'is_trained') and ensemble_model.is_trained:
            try:
                ensemble_pred = ensemble_model.predict(data.tail(100))  # آخر 100 نقطة
                if ensemble_pred is not None and len(ensemble_pred) > 0:
                    base_pred = float(ensemble_pred[-1])
                    predictions['ensemble'] = {
                        1: base_pred,
                        3: base_pred * 1.005,  # تقدير محافظ
                        7: base_pred * 1.01,
                        30: base_pred * 1.02
                    }
                    logger.info(f"تم إنشاء تنبؤات Ensemble لـ {symbol}")
            except Exception as e:
                logger.error(f"خطأ في تنبؤات Ensemble لـ {symbol}: {e}")

        # إضافة معلومات إضافية
        predictions['current_price'] = float(current_price)
        predictions['timestamp'] = datetime.now().isoformat()
        predictions['symbol'] = symbol

        return predictions

    except Exception as e:
        logger.error(f"خطأ عام في إنشاء التنبؤات لـ {symbol}: {e}")
        return None

@app.route('/')
@app.route('/index')
@app.route('/dashboard')
def index():
    """الصفحة الرئيسية - لوحة التحكم"""
    return render_template('dashboard.html')

@app.route('/analysis')
def analysis():
    """صفحة التحليل المتقدم"""
    return render_template('analysis.html')

@app.route('/predictions')
def predictions():
    """صفحة التنبؤات المفصلة"""
    return render_template('predictions.html')

@app.route('/models')
def models_page():
    """صفحة إدارة النماذج"""
    return render_template('models.html')

@app.route('/api/symbols')
def get_symbols():
    """الحصول على قائمة العملات المدعومة"""
    return jsonify({
        'symbols': Config.CRYPTO_SYMBOLS,
        'default': Config.DEFAULT_SYMBOL
    })

@app.route('/api/data/<symbol>')
def get_data(symbol):
    """الحصول على بيانات عملة معينة"""
    try:
        if symbol not in Config.CRYPTO_SYMBOLS:
            return jsonify({'error': 'عملة غير مدعومة'}), 400
        
        if symbol in latest_data:
            data = latest_data[symbol]
            
            # تحويل البيانات التاريخية إلى JSON
            historical_json = data['historical'].tail(100).to_dict('records')
            
            return jsonify({
                'symbol': symbol,
                'historical_data': historical_json,
                'real_time_data': data['real_time'],
                'sentiment_score': data['sentiment'],
                'last_updated': data['last_updated'].isoformat()
            })
        else:
            return jsonify({'error': 'البيانات غير متاحة'}), 404
            
    except Exception as e:
        logger.error(f"خطأ في الحصول على البيانات: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

@app.route('/api/predict/<symbol>')
def get_predictions(symbol):
    """الحصول على التنبؤات لعملة معينة"""
    try:
        if symbol not in Config.CRYPTO_SYMBOLS:
            return jsonify({'error': 'عملة غير مدعومة'}), 400
        
        if symbol in latest_predictions and latest_predictions[symbol]:
            predictions = latest_predictions[symbol]
            
            # إضافة تحليل الاتجاه
            current_price = predictions['current_price']
            
            analysis = {}
            for model_type in ['lstm', 'ensemble']:
                if model_type in predictions:
                    model_predictions = predictions[model_type]
                    analysis[model_type] = {}
                    
                    for horizon, pred_price in model_predictions.items():
                        trend, emoji = get_trend_direction(current_price, pred_price)
                        change_pct = calculate_percentage_change(current_price, pred_price)
                        
                        analysis[model_type][f'{horizon}d'] = {
                            'predicted_price': pred_price,
                            'formatted_price': format_currency(pred_price),
                            'change_percentage': change_pct,
                            'trend': trend,
                            'emoji': emoji
                        }
            
            return jsonify({
                'symbol': symbol,
                'current_price': current_price,
                'formatted_current_price': format_currency(current_price),
                'predictions': analysis,
                'timestamp': predictions['timestamp']
            })
        else:
            return jsonify({'error': 'التنبؤات غير متاحة'}), 404
            
    except Exception as e:
        logger.error(f"خطأ في الحصول على التنبؤات: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

@app.route('/api/train/<symbol>', methods=['POST'])
def train_model(symbol):
    """تدريب النموذج لعملة معينة"""
    try:
        if symbol not in Config.CRYPTO_SYMBOLS:
            return jsonify({'error': 'عملة غير مدعومة'}), 400
        
        # جمع البيانات
        logger.info(f"بدء تدريب النموذج لـ {symbol}")
        
        historical_data = data_collector.get_historical_data(symbol)
        if historical_data.empty:
            return jsonify({'error': 'لا توجد بيانات كافية'}), 400
        
        # هندسة الخصائص
        sentiment_score = data_collector.get_sentiment_score(symbol)
        featured_data = feature_engineer.create_features(historical_data, sentiment_score)
        
        if len(featured_data) < Config.SEQUENCE_LENGTH + 100:
            return jsonify({'error': 'البيانات غير كافية للتدريب'}), 400
        
        # تدريب النموذج
        lstm_model = models[symbol]['lstm']
        lstm_model.n_features = len(featured_data.columns)
        lstm_model.build_model('standard')
        
        # تحضير البيانات
        X, y = lstm_model.prepare_data(featured_data)
        
        if X is None or len(X) == 0:
            return jsonify({'error': 'فشل في تحضير البيانات'}), 400
        
        # تقسيم البيانات
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        # التدريب
        history = lstm_model.train(X_train, y_train, X_val, y_val)
        
        if history is not None:
            # حفظ النموذج
            model_path = Config.get_model_path(symbol, 'lstm')
            lstm_model.save_model(model_path)
            
            # تقييم النموذج
            metrics = lstm_model.evaluate(X_val, y_val)
            
            logger.info(f"تم تدريب النموذج بنجاح لـ {symbol}")
            
            return jsonify({
                'message': f'تم تدريب النموذج بنجاح لـ {symbol}',
                'metrics': metrics,
                'training_samples': len(X_train),
                'validation_samples': len(X_val)
            })
        else:
            return jsonify({'error': 'فشل في التدريب'}), 500
            
    except Exception as e:
        logger.error(f"خطأ في تدريب النموذج: {e}")
        return jsonify({'error': f'خطأ في التدريب: {str(e)}'}), 500

@app.route('/api/status')
def get_status():
    """الحصول على حالة النظام"""
    try:
        status = {
            'system_status': 'نشط',
            'models_initialized': len(models),
            'supported_symbols': Config.CRYPTO_SYMBOLS,
            'last_update': datetime.now().isoformat(),
            'data_status': {},
            'model_status': {}
        }
        
        # حالة البيانات
        for symbol in Config.CRYPTO_SYMBOLS:
            if symbol in latest_data:
                status['data_status'][symbol] = {
                    'available': True,
                    'last_updated': latest_data[symbol]['last_updated'].isoformat(),
                    'records_count': len(latest_data[symbol]['historical'])
                }
            else:
                status['data_status'][symbol] = {'available': False}
        
        # حالة النماذج
        for symbol in Config.CRYPTO_SYMBOLS:
            if symbol in models:
                lstm_trained = models[symbol]['lstm'].model is not None
                ensemble_trained = models[symbol]['ensemble'].is_trained
                
                status['model_status'][symbol] = {
                    'lstm_trained': lstm_trained,
                    'ensemble_trained': ensemble_trained
                }
            else:
                status['model_status'][symbol] = {
                    'lstm_trained': False,
                    'ensemble_trained': False
                }
        
        return jsonify(status)

    except Exception as e:
        logger.error(f"خطأ في الحصول على الحالة: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

@app.route('/api/analysis/<symbol>')
def get_analysis(symbol):
    """الحصول على التحليل المتقدم لعملة معينة"""
    try:
        if symbol not in Config.CRYPTO_SYMBOLS:
            return jsonify({'error': 'عملة غير مدعومة'}), 400

        if symbol not in latest_data:
            return jsonify({'error': 'البيانات غير متاحة'}), 404

        data = latest_data[symbol]['historical']

        # حساب التحليل الفني
        analysis = {
            'symbol': symbol,
            'technical_analysis': {},
            'price_analysis': {},
            'volume_analysis': {},
            'trend_analysis': {}
        }

        # التحليل الفني
        latest_data_point = data.iloc[-1]

        if 'rsi_14' in data.columns:
            rsi = latest_data_point['rsi_14']
            if rsi > 70:
                rsi_signal = 'بيع'
            elif rsi < 30:
                rsi_signal = 'شراء'
            else:
                rsi_signal = 'محايد'

            analysis['technical_analysis']['rsi'] = {
                'value': float(rsi),
                'signal': rsi_signal
            }

        if 'macd' in data.columns and 'macd_signal' in data.columns:
            macd = latest_data_point['macd']
            macd_signal = latest_data_point['macd_signal']
            macd_histogram = macd - macd_signal

            analysis['technical_analysis']['macd'] = {
                'macd': float(macd),
                'signal': float(macd_signal),
                'histogram': float(macd_histogram),
                'trend': 'صاعد' if macd_histogram > 0 else 'هابط'
            }

        # تحليل السعر
        price_data = data['close'].tail(30)
        analysis['price_analysis'] = {
            'current_price': float(data['close'].iloc[-1]),
            'price_change_24h': float(data['close'].iloc[-1] - data['close'].iloc[-2]),
            'price_change_7d': float(data['close'].iloc[-1] - data['close'].iloc[-7]) if len(data) >= 7 else 0,
            'volatility_30d': float(price_data.std()),
            'support_level': float(data['low'].tail(20).min()),
            'resistance_level': float(data['high'].tail(20).max())
        }

        # تحليل الحجم
        volume_data = data['volume'].tail(30)
        analysis['volume_analysis'] = {
            'current_volume': float(data['volume'].iloc[-1]),
            'avg_volume_30d': float(volume_data.mean()),
            'volume_trend': 'مرتفع' if data['volume'].iloc[-1] > volume_data.mean() else 'منخفض'
        }

        # تحليل الاتجاه
        sma_20 = data['sma_20'].iloc[-1] if 'sma_20' in data.columns else data['close'].iloc[-1]
        sma_50 = data['sma_50'].iloc[-1] if 'sma_50' in data.columns else data['close'].iloc[-1]
        current_price = data['close'].iloc[-1]

        if current_price > sma_20 > sma_50:
            trend = 'صاعد قوي'
        elif current_price > sma_20:
            trend = 'صاعد'
        elif current_price < sma_20 < sma_50:
            trend = 'هابط قوي'
        else:
            trend = 'هابط'

        analysis['trend_analysis'] = {
            'overall_trend': trend,
            'sma_20': float(sma_20),
            'sma_50': float(sma_50),
            'price_vs_sma20': float((current_price - sma_20) / sma_20 * 100)
        }

        return jsonify(analysis)

    except Exception as e:
        logger.error(f"خطأ في التحليل: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

@app.route('/api/compare')
def compare_symbols():
    """مقارنة العملات المختلفة"""
    try:
        comparison = {}

        for symbol in Config.CRYPTO_SYMBOLS:
            if symbol in latest_data and symbol in latest_predictions:
                data = latest_data[symbol]
                predictions = latest_predictions[symbol]

                current_price = data['real_time_data'].get('current_price', 0)
                previous_close = data['real_time_data'].get('previous_close', current_price)
                change_24h = ((current_price - previous_close) / previous_close * 100) if previous_close > 0 else 0

                # التنبؤ لـ 7 أيام
                pred_7d = 0
                if 'lstm' in predictions and 7 in predictions['lstm']:
                    pred_7d = predictions['lstm'][7]
                elif 'ensemble' in predictions and 7 in predictions['ensemble']:
                    pred_7d = predictions['ensemble'][7]

                pred_change_7d = ((pred_7d - current_price) / current_price * 100) if current_price > 0 and pred_7d > 0 else 0

                comparison[symbol] = {
                    'name': get_symbol_name(symbol),
                    'current_price': current_price,
                    'change_24h': change_24h,
                    'predicted_7d': pred_7d,
                    'predicted_change_7d': pred_change_7d,
                    'sentiment': data.get('sentiment', 0),
                    'volume': data['real_time_data'].get('volume', 0)
                }

        return jsonify(comparison)

    except Exception as e:
        logger.error(f"خطأ في المقارنة: {e}")
        return jsonify({'error': 'خطأ في الخادم'}), 500

def get_symbol_name(symbol):
    """الحصول على اسم العملة"""
    symbol_names = {
        'BTC-USD': 'بيتكوين',
        'ETH-USD': 'إيثيريوم',
        'ADA-USD': 'كاردانو',
        'DOT-USD': 'بولكادوت',
        'LINK-USD': 'تشين لينك'
    }
    return symbol_names.get(symbol, symbol)

if __name__ == '__main__':
    # تهيئة النماذج
    initialize_models()
    
    # بدء خيط تحديث البيانات
    update_thread = threading.Thread(target=update_data_and_predictions, daemon=True)
    update_thread.start()
    
    # تشغيل التطبيق
    logger.info(f"تشغيل التطبيق على {Config.HOST}:{Config.PORT}")
    app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG)
