"""
نموذج LSTM المتقدم للتنبؤ بأسعار العملات المشفرة
Advanced LSTM Model for Cryptocurrency Price Forecasting
"""

import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, Attention, MultiHeadAttention
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from sklearn.metrics import mean_squared_error, mean_absolute_error
import joblib
import os
from datetime import datetime
import logging

from config import Config, ModelConfig
from utils import logger, create_sequences, create_multi_horizon_sequences, calculate_metrics

class LSTMPredictor:
    """نموذج LSTM المتقدم للتنبؤ"""
    
    def __init__(self, sequence_length=None, n_features=None, horizons=None):
        self.logger = logger
        self.sequence_length = sequence_length or Config.SEQUENCE_LENGTH
        self.n_features = n_features
        self.horizons = horizons or Config.PREDICTION_HORIZONS
        self.model = None
        self.scaler = None
        self.history = None
        
    def build_model(self, model_type='standard'):
        """بناء نموذج LSTM"""
        try:
            self.logger.info(f"بناء نموذج LSTM من نوع: {model_type}")
            
            if model_type == 'standard':
                self.model = self._build_standard_lstm()
            elif model_type == 'multi_horizon':
                self.model = self._build_multi_horizon_lstm()
            elif model_type == 'attention':
                self.model = self._build_attention_lstm()
            else:
                raise ValueError(f"نوع النموذج غير مدعوم: {model_type}")
            
            self.logger.info("تم بناء النموذج بنجاح")
            return self.model
            
        except Exception as e:
            self.logger.error(f"خطأ في بناء النموذج: {e}")
            return None
    
    def _build_standard_lstm(self):
        """بناء نموذج LSTM قياسي"""
        model = Sequential([
            LSTM(
                Config.LSTM_UNITS_1,
                return_sequences=True,
                input_shape=(self.sequence_length, self.n_features)
            ),
            Dropout(Config.DROPOUT_RATE),
            
            LSTM(
                Config.LSTM_UNITS_2,
                return_sequences=False
            ),
            Dropout(Config.DROPOUT_RATE),
            
            Dense(25, activation='relu'),
            Dense(1, activation='linear')
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=Config.LEARNING_RATE),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def _build_multi_horizon_lstm(self):
        """بناء نموذج LSTM متعدد الآفاق"""
        input_layer = Input(shape=(self.sequence_length, self.n_features))
        
        # طبقات LSTM
        lstm1 = LSTM(Config.LSTM_UNITS_1, return_sequences=True)(input_layer)
        dropout1 = Dropout(Config.DROPOUT_RATE)(lstm1)
        
        lstm2 = LSTM(Config.LSTM_UNITS_2, return_sequences=False)(dropout1)
        dropout2 = Dropout(Config.DROPOUT_RATE)(lstm2)
        
        # طبقة مشتركة
        dense_shared = Dense(50, activation='relu')(dropout2)
        
        # مخرجات متعددة للآفاق المختلفة
        outputs = []
        for i, horizon in enumerate(self.horizons):
            output = Dense(25, activation='relu', name=f'dense_{horizon}d')(dense_shared)
            output = Dense(1, activation='linear', name=f'output_{horizon}d')(output)
            outputs.append(output)
        
        model = Model(inputs=input_layer, outputs=outputs)
        
        # تجميع النموذج مع خسائر متعددة
        losses = {f'output_{horizon}d': 'mse' for horizon in self.horizons}
        model.compile(
            optimizer=Adam(learning_rate=Config.LEARNING_RATE),
            loss=losses,
            metrics=['mae']
        )
        
        return model
    
    def _build_attention_lstm(self):
        """بناء نموذج LSTM مع آلية الانتباه"""
        input_layer = Input(shape=(self.sequence_length, self.n_features))
        
        # طبقات LSTM
        lstm1 = LSTM(Config.LSTM_UNITS_1, return_sequences=True)(input_layer)
        dropout1 = Dropout(Config.DROPOUT_RATE)(lstm1)
        
        # طبقة الانتباه
        attention = MultiHeadAttention(
            num_heads=4,
            key_dim=Config.LSTM_UNITS_1 // 4
        )(dropout1, dropout1)
        
        lstm2 = LSTM(Config.LSTM_UNITS_2, return_sequences=False)(attention)
        dropout2 = Dropout(Config.DROPOUT_RATE)(lstm2)
        
        # طبقات الإخراج
        dense1 = Dense(25, activation='relu')(dropout2)
        output = Dense(1, activation='linear')(dense1)
        
        model = Model(inputs=input_layer, outputs=output)
        
        model.compile(
            optimizer=Adam(learning_rate=Config.LEARNING_RATE),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def prepare_data(self, data, target_column='close', multi_horizon=False):
        """تحضير البيانات للتدريب"""
        try:
            self.logger.info("تحضير البيانات للتدريب...")
            
            # تحديد عدد الخصائص
            if self.n_features is None:
                self.n_features = len(data.columns)
            
            if multi_horizon:
                X, y = create_multi_horizon_sequences(
                    data, self.sequence_length, self.horizons, target_column
                )
            else:
                X, y = create_sequences(data, self.sequence_length, target_column)
            
            self.logger.info(f"شكل البيانات: X={X.shape}, y={y.shape}")
            return X, y
            
        except Exception as e:
            self.logger.error(f"خطأ في تحضير البيانات: {e}")
            return None, None
    
    def train(self, X_train, y_train, X_val=None, y_val=None, save_path=None):
        """تدريب النموذج"""
        try:
            self.logger.info("بدء تدريب النموذج...")
            
            if self.model is None:
                raise ValueError("يجب بناء النموذج أولاً")
            
            # إعداد callbacks
            callbacks = [
                EarlyStopping(
                    monitor='val_loss',
                    patience=10,
                    restore_best_weights=True
                ),
                ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.5,
                    patience=5,
                    min_lr=1e-7
                )
            ]
            
            if save_path:
                callbacks.append(
                    ModelCheckpoint(
                        save_path,
                        monitor='val_loss',
                        save_best_only=True,
                        save_weights_only=False
                    )
                )
            
            # تحديد بيانات التحقق
            if X_val is None or y_val is None:
                validation_data = None
                validation_split = Config.VALIDATION_SPLIT
            else:
                validation_data = (X_val, y_val)
                validation_split = None
            
            # التدريب
            self.history = self.model.fit(
                X_train, y_train,
                batch_size=Config.BATCH_SIZE,
                epochs=Config.EPOCHS,
                validation_split=validation_split,
                validation_data=validation_data,
                callbacks=callbacks,
                verbose=1
            )
            
            self.logger.info("تم الانتهاء من التدريب")
            return self.history
            
        except Exception as e:
            self.logger.error(f"خطأ في التدريب: {e}")
            return None
    
    def predict(self, X, return_sequences=False):
        """التنبؤ باستخدام النموذج"""
        try:
            if self.model is None:
                raise ValueError("النموذج غير مدرب")
            
            predictions = self.model.predict(X)
            
            if return_sequences and len(predictions.shape) == 2:
                return predictions
            
            return predictions.flatten() if len(predictions.shape) > 1 else predictions
            
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ: {e}")
            return None
    
    def predict_future(self, last_sequence, steps=1):
        """التنبؤ بالقيم المستقبلية"""
        try:
            predictions = []
            current_sequence = last_sequence.copy()

            for _ in range(steps):
                # التنبؤ بالخطوة التالية
                next_pred = self.model.predict(current_sequence.reshape(1, self.sequence_length, self.n_features), verbose=0)
                predictions.append(next_pred[0, 0] if len(next_pred.shape) > 1 else next_pred[0])

                # تحديث التسلسل (إزالة أول قيمة وإضافة التنبؤ الجديد)
                current_sequence = np.roll(current_sequence, -1, axis=0)
                # تحديث العمود الأخير (السعر) بالتنبؤ الجديد
                current_sequence[-1, -1] = predictions[-1]

            return np.array(predictions)

        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ المستقبلي: {e}")
            return None
    
    def evaluate(self, X_test, y_test):
        """تقييم النموذج"""
        try:
            predictions = self.predict(X_test)
            
            if predictions is None:
                return None
            
            metrics = calculate_metrics(y_test, predictions)
            
            self.logger.info("نتائج التقييم:")
            for metric, value in metrics.items():
                self.logger.info(f"{metric}: {value:.4f}")
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"خطأ في التقييم: {e}")
            return None
    
    def save_model(self, filepath):
        """حفظ النموذج"""
        try:
            if self.model is None:
                raise ValueError("لا يوجد نموذج للحفظ")
            
            self.model.save(filepath)
            self.logger.info(f"تم حفظ النموذج في: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ النموذج: {e}")
            return False
    
    def load_model(self, filepath):
        """تحميل النموذج"""
        try:
            self.model = tf.keras.models.load_model(filepath)
            self.logger.info(f"تم تحميل النموذج من: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل النموذج: {e}")
            return False
    
    def get_model_summary(self):
        """الحصول على ملخص النموذج"""
        if self.model is not None:
            return self.model.summary()
        return "لا يوجد نموذج"

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء نموذج تجريبي
    predictor = LSTMPredictor(sequence_length=60, n_features=20)
    
    # بناء النموذج
    model = predictor.build_model('standard')
    
    if model:
        print("تم بناء النموذج بنجاح")
        print(predictor.get_model_summary())
