@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🚀 النظام المتقدم للتنبؤ بأسعار العملات المشفرة 🚀        ║
echo ║         Advanced Cryptocurrency Price Forecasting            ║
echo ║                                                              ║
echo ║                    📦 التثبيت والتشغيل 📦                    ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 💡 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python موجود
echo.

echo 📦 تثبيت المتطلبات الأساسية...
echo.

echo تثبيت Flask...
pip install flask
if errorlevel 1 (
    echo ❌ فشل في تثبيت Flask
    pause
    exit /b 1
)

echo تثبيت pandas...
pip install pandas
if errorlevel 1 (
    echo ❌ فشل في تثبيت pandas
    pause
    exit /b 1
)

echo تثبيت numpy...
pip install numpy
if errorlevel 1 (
    echo ❌ فشل في تثبيت numpy
    pause
    exit /b 1
)

echo تثبيت flask-cors...
pip install flask-cors
if errorlevel 1 (
    echo ❌ فشل في تثبيت flask-cors
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المتطلبات الأساسية بنجاح!
echo.

echo 🚀 تشغيل النظام...
echo.
echo 🌐 سيتم فتح المتصفح تلقائياً على: http://localhost:5000
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo.

timeout /t 3 >nul

python quick_start.py

pause
