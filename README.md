# 🚀 النظام المتقدم للتنبؤ بأسعار العملات المشفرة
## Advanced Cryptocurrency Price Forecasting System

نظام ذكي متطور يستخدم تقنيات الذكاء الاصطناعي والتعلم العميق للتنبؤ بأسعار العملات المشفرة مع تحليل المشاعر والمؤشرات الفنية.

---

## 🌟 الميزات الرئيسية

### 📊 مصادر البيانات
- **البيانات التاريخية**: Open, High, Low, Close, Volume من yfinance
- **المؤشرات الفنية**: RSI, MACD, SMA/EMA, Bollinger Bands, Stochastic, ATR, OBV, VWAP
- **تحليل المشاعر**: استخدام FinBERT لتحليل مشاعر الأخبار والتغريدات
- **البيانات الفورية**: أسعار وأحجام التداول في الوقت الفعلي

### 🧠 نماذج التنبؤ المتقدمة
- **LSTM Neural Network**: شبكة عصبية متقدمة مع طبقات 64→32 وحدة
- **Ensemble Models**: تجميع LSTM + XGBoost + Random Forest + ARIMA
- **Multi-Horizon Forecasting**: تنبؤات لـ 1، 3، 7، 30 يوم
- **Attention Mechanism**: آلية انتباه لتحسين دقة التنبؤات

### 🔧 معالجة البيانات المتقدمة
- **Feature Engineering**: هندسة خصائص متطورة مع 50+ مؤشر
- **Data Normalization**: تطبيع البيانات باستخدام MinMaxScaler
- **Sliding Window**: نوافذ زمنية منزلقة 60 يوم
- **Missing Data Handling**: معالجة ذكية للبيانات المفقودة

### 🖥️ واجهة المستخدم التفاعلية
- **Real-time Dashboard**: لوحة تحكم تفاعلية بالوقت الفعلي
- **Interactive Charts**: رسوم بيانية تفاعلية مع Chart.js
- **Multi-language Support**: دعم اللغة العربية والإنجليزية
- **Responsive Design**: تصميم متجاوب لجميع الأجهزة

---

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.9+**
- **TensorFlow/Keras** - للشبكات العصبية
- **scikit-learn** - للتعلم الآلي
- **pandas & numpy** - لمعالجة البيانات
- **Flask/FastAPI** - لواجهة برمجة التطبيقات
- **yfinance** - لجمع البيانات المالية
- **transformers** - لتحليل المشاعر

### Frontend
- **HTML5 & CSS3**
- **JavaScript (ES6+)**
- **Bootstrap 5** - للتصميم
- **Chart.js** - للرسوم البيانية
- **Font Awesome** - للأيقونات

### Machine Learning
- **LSTM Networks** - للتنبؤ بالسلاسل الزمنية
- **XGBoost** - للتعلم المعزز
- **Random Forest** - للتجميع
- **ARIMA** - للنماذج التقليدية
- **FinBERT** - لتحليل المشاعر

---

## 📦 التثبيت والإعداد

### 1. متطلبات النظام
```bash
Python 3.9+
pip (Python package manager)
Git
```

### 2. تحميل المشروع
```bash
git clone <repository-url>
cd LSTM
```

### 3. إنشاء البيئة الافتراضية
```bash
python -m venv crypto_env
# Windows
crypto_env\Scripts\activate
# Linux/Mac
source crypto_env/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. إعداد متغيرات البيئة (اختياري)
```bash
# إنشاء ملف .env
echo "SECRET_KEY=your-secret-key-here" > .env
echo "TWITTER_API_KEY=your-twitter-api-key" >> .env
echo "NEWS_API_KEY=your-news-api-key" >> .env
```

---

## 🚀 تشغيل النظام

### 1. تشغيل التطبيق
```bash
python app.py
```

### 2. فتح المتصفح
```
http://localhost:5000
```

### 3. استخدام النظام
1. **اختيار العملة**: من القائمة المنسدلة
2. **عرض البيانات**: مراقبة الأسعار والمؤشرات
3. **تدريب النموذج**: اختيار عملة وبدء التدريب
4. **مراقبة التنبؤات**: عرض التنبؤات المختلفة

---

## 📈 استخدام واجهة برمجة التطبيقات

### الحصول على قائمة العملات
```bash
GET /api/symbols
```

### الحصول على بيانات عملة
```bash
GET /api/data/BTC-USD
```

### الحصول على التنبؤات
```bash
GET /api/predict/BTC-USD
```

### تدريب نموذج
```bash
POST /api/train/BTC-USD
```

### حالة النظام
```bash
GET /api/status
```

---

## 🔧 التخصيص والإعدادات

### تعديل الإعدادات في `config.py`:

```python
# العملات المدعومة
CRYPTO_SYMBOLS = ['BTC-USD', 'ETH-USD', 'ADA-USD']

# إعدادات النموذج
LSTM_UNITS_1 = 64
LSTM_UNITS_2 = 32
SEQUENCE_LENGTH = 60
PREDICTION_HORIZONS = [1, 3, 7, 30]

# إعدادات التحديث
UPDATE_INTERVAL_MINUTES = 60
RETRAIN_INTERVAL_DAYS = 7
```

---

## 📊 مثال على الاستخدام البرمجي

```python
from data_collector import CryptoDataCollector
from feature_engineering import FeatureEngineer
from models.lstm_model import LSTMPredictor

# جمع البيانات
collector = CryptoDataCollector()
data = collector.get_historical_data('BTC-USD')

# هندسة الخصائص
engineer = FeatureEngineer()
featured_data = engineer.create_features(data)

# تدريب النموذج
model = LSTMPredictor()
model.n_features = len(featured_data.columns)
model.build_model('standard')

# تحضير البيانات
X, y = model.prepare_data(featured_data)

# التدريب
history = model.train(X, y)

# التنبؤ
predictions = model.predict_future(X[-1], steps=7)
print(f"التنبؤات للأسبوع القادم: {predictions}")
```

---

## 🎯 الأداء والدقة

### مقاييس الأداء المتوقعة:
- **RMSE**: < 5% من متوسط السعر
- **MAE**: < 3% من متوسط السعر
- **R²**: > 0.85 للتنبؤات قصيرة المدى
- **MAPE**: < 10% للتنبؤات اليومية

### تحسينات الأداء:
- **GPU Acceleration**: دعم CUDA للتدريب السريع
- **Model Caching**: حفظ النماذج المدربة
- **Data Caching**: تخزين مؤقت للبيانات
- **Async Processing**: معالجة غير متزامنة

---

## 🔒 الأمان والموثوقية

- **Input Validation**: التحقق من صحة المدخلات
- **Error Handling**: معالجة شاملة للأخطاء
- **Rate Limiting**: تحديد معدل الطلبات
- **Data Encryption**: تشفير البيانات الحساسة
- **Backup System**: نظام النسخ الاحتياطي

---

## 🤝 المساهمة في المشروع

1. **Fork** المشروع
2. إنشاء **branch** جديد (`git checkout -b feature/amazing-feature`)
3. **Commit** التغييرات (`git commit -m 'Add amazing feature'`)
4. **Push** إلى البرانش (`git push origin feature/amazing-feature`)
5. فتح **Pull Request**

---

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: [docs.crypto-forecasting.com](https://docs.crypto-forecasting.com)
- **المجتمع**: [Discord Server](https://discord.gg/crypto-forecasting)

---

## 🙏 شكر وتقدير

- **TensorFlow Team** - للمكتبة الرائعة
- **yfinance** - لتوفير البيانات المالية
- **Hugging Face** - لنماذج تحليل المشاعر
- **Chart.js** - للرسوم البيانية التفاعلية

---

**تم تطوير هذا النظام بـ ❤️ للمجتمع العربي والعالمي**
