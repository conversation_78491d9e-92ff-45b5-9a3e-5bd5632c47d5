/**
 * نظام التنقل المشترك للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
 * Shared Navigation System for Advanced Cryptocurrency Price Forecasting System
 */

class NavigationSystem {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.init();
    }
    
    init() {
        this.setupPageTransitions();
        this.setupActiveNavigation();
        this.setupGlobalEventListeners();
        this.checkSystemStatus();
    }
    
    getCurrentPage() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index') return 'dashboard';
        if (path === '/predictions') return 'predictions';
        if (path === '/analysis') return 'analysis';
        if (path === '/models') return 'models';
        return 'dashboard';
    }
    
    setupActiveNavigation() {
        // تحديث الروابط النشطة
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            const href = link.getAttribute('href');
            if (
                (this.currentPage === 'dashboard' && (href === '/' || href.includes('index'))) ||
                (this.currentPage === 'predictions' && href.includes('predictions')) ||
                (this.currentPage === 'analysis' && href.includes('analysis')) ||
                (this.currentPage === 'models' && href.includes('models'))
            ) {
                link.classList.add('active');
            }
        });
    }
    
    setupPageTransitions() {
        // إضافة تأثيرات انتقال بين الصفحات
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                
                // تجاهل الروابط الخارجية أو المرساة
                if (href.startsWith('http') || href.startsWith('#')) {
                    return;
                }
                
                // إظهار مؤشر التحميل
                this.showPageLoading();
                
                // السماح للرابط بالعمل بشكل طبيعي
                // مؤشر التحميل سيختفي عند تحميل الصفحة الجديدة
            });
        });
    }
    
    setupGlobalEventListeners() {
        // إخفاء مؤشر التحميل عند تحميل الصفحة
        window.addEventListener('load', () => {
            this.hidePageLoading();
        });
        
        // معالجة أخطاء التحميل
        window.addEventListener('error', (e) => {
            this.hidePageLoading();
            console.error('خطأ في تحميل الصفحة:', e);
        });
        
        // تحديث الحالة كل دقيقة
        setInterval(() => {
            this.updateSystemStatus();
        }, 60000);
    }
    
    showPageLoading() {
        // إنشاء مؤشر تحميل إذا لم يكن موجوداً
        let loadingDiv = document.getElementById('pageLoading');
        
        if (!loadingDiv) {
            loadingDiv = document.createElement('div');
            loadingDiv.id = 'pageLoading';
            loadingDiv.className = 'page-loading';
            loadingDiv.innerHTML = `
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <div class="mt-3">
                        <h5>جاري التحميل...</h5>
                        <p class="text-muted">يرجى الانتظار</p>
                    </div>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }
        
        loadingDiv.style.display = 'flex';
    }
    
    hidePageLoading() {
        const loadingDiv = document.getElementById('pageLoading');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }
    
    async checkSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            this.updateSystemIndicator(status.system_status === 'نشط');
            
        } catch (error) {
            console.warn('لا يمكن التحقق من حالة النظام:', error);
            this.updateSystemIndicator(false);
        }
    }
    
    async updateSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            this.updateSystemIndicator(status.system_status === 'نشط');
            
            // تحديث آخر وقت تحديث إذا كان موجوداً
            const lastUpdateElement = document.getElementById('lastUpdate');
            if (lastUpdateElement) {
                lastUpdateElement.textContent = new Date().toLocaleString('ar-SA');
            }
            
        } catch (error) {
            console.warn('خطأ في تحديث حالة النظام:', error);
            this.updateSystemIndicator(false);
        }
    }
    
    updateSystemIndicator(isOnline) {
        // البحث عن مؤشر الحالة أو إنشاؤه
        let statusIndicator = document.querySelector('.system-status-indicator');
        
        if (!statusIndicator) {
            // إنشاء مؤشر الحالة في شريط التنقل
            const navbar = document.querySelector('.navbar-brand');
            if (navbar) {
                statusIndicator = document.createElement('span');
                statusIndicator.className = 'system-status-indicator status-indicator ms-2';
                navbar.appendChild(statusIndicator);
            }
        }
        
        if (statusIndicator) {
            statusIndicator.className = `system-status-indicator status-indicator ms-2 ${isOnline ? 'online' : 'offline'}`;
            statusIndicator.title = isOnline ? 'النظام متصل' : 'النظام غير متصل';
        }
    }
    
    // دوال مساعدة للصفحات الأخرى
    static showAlert(message, type = 'info', duration = 5000) {
        // إنشاء تنبيه Bootstrap
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
        alertDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas ${NavigationSystem.getAlertIcon(type)} me-2"></i>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // إزالة التنبيه تلقائياً
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
        
        return alertDiv;
    }
    
    static getAlertIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'danger': 'fa-exclamation-triangle',
            'warning': 'fa-exclamation-circle',
            'info': 'fa-info-circle',
            'primary': 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }
    
    static formatCurrency(value, currency = 'USD') {
        if (currency === 'USD') {
            return `$${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        }
        return `${value.toLocaleString()} ${currency}`;
    }
    
    static formatPercentage(value, decimals = 2) {
        return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
    }
    
    static formatVolume(volume) {
        if (volume >= 1e12) return (volume / 1e12).toFixed(2) + 'T';
        if (volume >= 1e9) return (volume / 1e9).toFixed(2) + 'B';
        if (volume >= 1e6) return (volume / 1e6).toFixed(2) + 'M';
        if (volume >= 1e3) return (volume / 1e3).toFixed(2) + 'K';
        return volume.toLocaleString();
    }
    
    static getSymbolName(symbol) {
        const symbolNames = {
            'BTC-USD': 'بيتكوين',
            'ETH-USD': 'إيثيريوم',
            'ADA-USD': 'كاردانو',
            'DOT-USD': 'بولكادوت',
            'LINK-USD': 'تشين لينك'
        };
        return symbolNames[symbol] || symbol;
    }
    
    static async fetchWithErrorHandling(url, options = {}) {
        try {
            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
            
        } catch (error) {
            console.error(`خطأ في الطلب إلى ${url}:`, error);
            NavigationSystem.showAlert(`خطأ في تحميل البيانات: ${error.message}`, 'danger');
            throw error;
        }
    }
}

// تهيئة نظام التنقل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.navigationSystem = new NavigationSystem();
});

// تصدير الفئة للاستخدام في الصفحات الأخرى
window.NavigationSystem = NavigationSystem;
