/**
 * صفحة التحليل المتقدم للعملات المشفرة
 * Advanced Analysis Page for Cryptocurrency Forecasting
 */

class AnalysisPage {
    constructor() {
        this.currentSymbol = null;
        this.charts = {};
        this.analysisData = null;
        
        this.init();
    }
    
    async init() {
        console.log('تهيئة صفحة التحليل...');
        
        // تحميل قائمة العملات
        await this.loadSymbols();
        
        // تهيئة الأحداث
        this.setupEventListeners();
        
        // تحميل مقارنة العملات
        await this.loadComparison();
        
        console.log('تم تهيئة صفحة التحليل بنجاح');
    }
    
    async loadSymbols() {
        try {
            const response = await fetch('/api/symbols');
            const data = await response.json();
            
            const symbolSelect = document.getElementById('analysisSymbolSelect');
            symbolSelect.innerHTML = '<option value="">اختر العملة للتحليل...</option>';
            
            data.symbols.forEach(symbol => {
                const option = new Option(this.getSymbolName(symbol), symbol);
                symbolSelect.add(option);
            });
            
        } catch (error) {
            console.error('خطأ في تحميل قائمة العملات:', error);
        }
    }
    
    setupEventListeners() {
        // تغيير العملة
        document.getElementById('analysisSymbolSelect').addEventListener('change', (e) => {
            if (e.target.value) {
                this.currentSymbol = e.target.value;
                this.loadAnalysis();
            }
        });
        
        // زر التحديث
        document.getElementById('refreshAnalysisBtn').addEventListener('click', () => {
            if (this.currentSymbol) {
                this.loadAnalysis();
            }
        });
    }
    
    async loadAnalysis() {
        if (!this.currentSymbol) return;
        
        try {
            this.showLoading();
            
            const response = await fetch(`/api/analysis/${this.currentSymbol}`);
            
            if (!response.ok) {
                throw new Error('فشل في تحميل التحليل');
            }
            
            this.analysisData = await response.json();
            this.displayAnalysis();
            this.showContent();
            
        } catch (error) {
            console.error('خطأ في تحميل التحليل:', error);
            this.showError();
        }
    }
    
    displayAnalysis() {
        if (!this.analysisData) return;
        
        // عرض التحليل الفني
        this.displayTechnicalAnalysis();
        
        // عرض تحليل السعر
        this.displayPriceAnalysis();
        
        // عرض تحليل الحجم
        this.displayVolumeAnalysis();
        
        // عرض تحليل الاتجاه
        this.displayTrendAnalysis();
        
        // تحديث الرسوم البيانية
        this.updateCharts();
    }
    
    displayTechnicalAnalysis() {
        const container = document.getElementById('technicalAnalysis');
        const technical = this.analysisData.technical_analysis;
        
        let html = '';
        
        // مؤشر RSI
        if (technical.rsi) {
            const rsi = technical.rsi;
            const rsiColor = this.getRSIColor(rsi.value);
            
            html += `
                <div class="analysis-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="fw-bold">مؤشر القوة النسبية (RSI)</span>
                        <span class="badge" style="background-color: ${rsiColor}">${rsi.value.toFixed(2)}</span>
                    </div>
                    <div class="small text-muted mt-1">
                        الإشارة: <span class="fw-bold">${rsi.signal}</span>
                    </div>
                    <div class="progress mt-2" style="height: 8px;">
                        <div class="progress-bar" style="width: ${rsi.value}%; background-color: ${rsiColor}"></div>
                    </div>
                </div>
            `;
        }
        
        // مؤشر MACD
        if (technical.macd) {
            const macd = technical.macd;
            const macdColor = macd.histogram > 0 ? '#10b981' : '#ef4444';
            
            html += `
                <div class="analysis-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="fw-bold">مؤشر MACD</span>
                        <span class="badge" style="background-color: ${macdColor}">${macd.trend}</span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-4">
                            <small class="text-muted">MACD</small>
                            <div class="fw-bold ltr-numbers">${macd.macd.toFixed(4)}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Signal</small>
                            <div class="fw-bold ltr-numbers">${macd.signal.toFixed(4)}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Histogram</small>
                            <div class="fw-bold ltr-numbers">${macd.histogram.toFixed(4)}</div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        if (html === '') {
            html = '<div class="text-center text-muted">لا توجد بيانات تحليل فني متاحة</div>';
        }
        
        container.innerHTML = html;
    }
    
    displayPriceAnalysis() {
        const container = document.getElementById('priceAnalysis');
        const price = this.analysisData.price_analysis;
        
        const change24h = price.price_change_24h;
        const change7d = price.price_change_7d;
        const changeColor24h = change24h >= 0 ? '#10b981' : '#ef4444';
        const changeColor7d = change7d >= 0 ? '#10b981' : '#ef4444';
        
        const html = `
            <div class="row">
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <div class="h4 mb-1 ltr-numbers">$${price.current_price.toLocaleString()}</div>
                        <small class="text-muted">السعر الحالي</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <div class="h5 mb-1 ltr-numbers" style="color: ${changeColor24h}">
                            ${change24h >= 0 ? '+' : ''}${change24h.toFixed(2)}
                        </div>
                        <small class="text-muted">التغيير 24 ساعة</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <div class="h5 mb-1 ltr-numbers">$${price.support_level.toLocaleString()}</div>
                        <small class="text-muted">مستوى الدعم</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <div class="h5 mb-1 ltr-numbers">$${price.resistance_level.toLocaleString()}</div>
                        <small class="text-muted">مستوى المقاومة</small>
                    </div>
                </div>
                <div class="col-12">
                    <div class="analysis-item">
                        <div class="d-flex justify-content-between">
                            <span>التقلبات (30 يوم)</span>
                            <span class="fw-bold ltr-numbers">${price.volatility_30d.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    displayVolumeAnalysis() {
        const container = document.getElementById('volumeAnalysis');
        const volume = this.analysisData.volume_analysis;
        
        const volumeRatio = volume.current_volume / volume.avg_volume_30d;
        const volumeColor = volumeRatio > 1.2 ? '#10b981' : volumeRatio < 0.8 ? '#ef4444' : '#f59e0b';
        
        const html = `
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="text-center">
                        <div class="h4 mb-1 ltr-numbers">${this.formatVolume(volume.current_volume)}</div>
                        <small class="text-muted">الحجم الحالي</small>
                    </div>
                </div>
                <div class="col-12 mb-3">
                    <div class="analysis-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>متوسط الحجم (30 يوم)</span>
                            <span class="fw-bold ltr-numbers">${this.formatVolume(volume.avg_volume_30d)}</span>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="analysis-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>اتجاه الحجم</span>
                            <span class="badge" style="background-color: ${volumeColor}">${volume.volume_trend}</span>
                        </div>
                        <div class="progress mt-2" style="height: 8px;">
                            <div class="progress-bar" style="width: ${Math.min(volumeRatio * 50, 100)}%; background-color: ${volumeColor}"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    displayTrendAnalysis() {
        const container = document.getElementById('trendAnalysis');
        const trend = this.analysisData.trend_analysis;
        
        const trendColor = this.getTrendColor(trend.overall_trend);
        const priceVsSMA = trend.price_vs_sma20;
        const smaColor = priceVsSMA > 0 ? '#10b981' : '#ef4444';
        
        const html = `
            <div class="row">
                <div class="col-12 mb-3">
                    <div class="text-center">
                        <div class="h4 mb-1" style="color: ${trendColor}">${trend.overall_trend}</div>
                        <small class="text-muted">الاتجاه العام</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="analysis-item">
                        <div class="text-center">
                            <div class="fw-bold ltr-numbers">$${trend.sma_20.toLocaleString()}</div>
                            <small class="text-muted">SMA 20</small>
                        </div>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="analysis-item">
                        <div class="text-center">
                            <div class="fw-bold ltr-numbers">$${trend.sma_50.toLocaleString()}</div>
                            <small class="text-muted">SMA 50</small>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="analysis-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>السعر مقابل SMA20</span>
                            <span class="fw-bold ltr-numbers" style="color: ${smaColor}">
                                ${priceVsSMA >= 0 ? '+' : ''}${priceVsSMA.toFixed(2)}%
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    async updateCharts() {
        // تحديث الرسم البياني الفني
        await this.updateTechnicalChart();
        
        // تحديث رسم الحجم
        await this.updateVolumeChart();
    }
    
    async updateTechnicalChart() {
        try {
            const response = await fetch(`/api/data/${this.currentSymbol}`);
            const data = await response.json();
            
            if (!data.historical_data) return;
            
            const ctx = document.getElementById('technicalChart').getContext('2d');
            const historicalData = data.historical_data.slice(-30);
            
            const labels = historicalData.map(item => {
                const date = new Date(item.date || item.Date);
                return date.toLocaleDateString('ar-SA');
            });
            
            const prices = historicalData.map(item => item.close || item.Close);
            const rsi = historicalData.map(item => item.rsi_14 || 50);
            
            if (this.charts.technicalChart) {
                this.charts.technicalChart.destroy();
            }
            
            this.charts.technicalChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'السعر',
                            data: prices,
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            yAxisID: 'y'
                        },
                        {
                            label: 'RSI',
                            data: rsi,
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: 'السعر ($)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            min: 0,
                            max: 100,
                            title: { display: true, text: 'RSI' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });
            
        } catch (error) {
            console.error('خطأ في تحديث الرسم الفني:', error);
        }
    }
    
    async updateVolumeChart() {
        try {
            const response = await fetch(`/api/data/${this.currentSymbol}`);
            const data = await response.json();
            
            if (!data.historical_data) return;
            
            const ctx = document.getElementById('volumeChart').getContext('2d');
            const historicalData = data.historical_data.slice(-30);
            
            const labels = historicalData.map(item => {
                const date = new Date(item.date || item.Date);
                return date.toLocaleDateString('ar-SA');
            });
            
            const volumes = historicalData.map(item => item.volume || item.Volume);
            
            if (this.charts.volumeChart) {
                this.charts.volumeChart.destroy();
            }
            
            this.charts.volumeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'الحجم',
                        data: volumes,
                        backgroundColor: 'rgba(100, 116, 139, 0.7)',
                        borderColor: '#64748b',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: 'الحجم' }
                        }
                    }
                }
            });
            
        } catch (error) {
            console.error('خطأ في تحديث رسم الحجم:', error);
        }
    }
    
    async loadComparison() {
        try {
            const response = await fetch('/api/compare');
            const data = await response.json();
            
            this.displayComparison(data);
            
        } catch (error) {
            console.error('خطأ في تحميل المقارنة:', error);
        }
    }
    
    displayComparison(data) {
        const tbody = document.querySelector('#comparisonTable tbody');
        
        let html = '';
        
        Object.entries(data).forEach(([symbol, info]) => {
            const change24hColor = info.change_24h >= 0 ? 'text-success' : 'text-danger';
            const predChangeColor = info.predicted_change_7d >= 0 ? 'text-success' : 'text-danger';
            const sentimentColor = this.getSentimentColor(info.sentiment);
            
            html += `
                <tr>
                    <td class="fw-bold">${info.name}</td>
                    <td class="ltr-numbers">$${info.current_price.toLocaleString()}</td>
                    <td class="${change24hColor} ltr-numbers">
                        ${info.change_24h >= 0 ? '+' : ''}${info.change_24h.toFixed(2)}%
                    </td>
                    <td class="ltr-numbers">$${info.predicted_7d.toLocaleString()}</td>
                    <td class="${predChangeColor} ltr-numbers">
                        ${info.predicted_change_7d >= 0 ? '+' : ''}${info.predicted_change_7d.toFixed(2)}%
                    </td>
                    <td>
                        <span class="badge" style="background-color: ${sentimentColor}">
                            ${info.sentiment.toFixed(2)}
                        </span>
                    </td>
                    <td class="ltr-numbers">${this.formatVolume(info.volume)}</td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html;
    }
    
    // Helper functions
    getRSIColor(value) {
        if (value > 70) return '#ef4444';
        if (value < 30) return '#10b981';
        return '#f59e0b';
    }
    
    getTrendColor(trend) {
        if (trend.includes('صاعد')) return '#10b981';
        if (trend.includes('هابط')) return '#ef4444';
        return '#f59e0b';
    }
    
    getSentimentColor(sentiment) {
        if (sentiment > 0.1) return '#10b981';
        if (sentiment < -0.1) return '#ef4444';
        return '#f59e0b';
    }
    
    formatVolume(volume) {
        if (volume >= 1e9) return (volume / 1e9).toFixed(2) + 'B';
        if (volume >= 1e6) return (volume / 1e6).toFixed(2) + 'M';
        if (volume >= 1e3) return (volume / 1e3).toFixed(2) + 'K';
        return volume.toLocaleString();
    }
    
    getSymbolName(symbol) {
        const names = {
            'BTC-USD': 'بيتكوين',
            'ETH-USD': 'إيثيريوم',
            'ADA-USD': 'كاردانو',
            'DOT-USD': 'بولكادوت',
            'LINK-USD': 'تشين لينك'
        };
        return names[symbol] || symbol;
    }
    
    showLoading() {
        document.getElementById('loadingState').classList.remove('d-none');
        document.getElementById('analysisContent').classList.add('d-none');
        document.getElementById('errorState').classList.add('d-none');
    }
    
    showContent() {
        document.getElementById('loadingState').classList.add('d-none');
        document.getElementById('analysisContent').classList.remove('d-none');
        document.getElementById('errorState').classList.add('d-none');
    }
    
    showError() {
        document.getElementById('loadingState').classList.add('d-none');
        document.getElementById('analysisContent').classList.add('d-none');
        document.getElementById('errorState').classList.remove('d-none');
    }
}

// Global function for retry button
function loadAnalysis() {
    if (window.analysisPage && window.analysisPage.currentSymbol) {
        window.analysisPage.loadAnalysis();
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.analysisPage = new AnalysisPage();
});
