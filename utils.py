"""
الأدوات المساعدة للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Utility Functions for Advanced Cryptocurrency Price Forecasting System
"""

import pandas as pd
import numpy as np
import logging
import os
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import json

def setup_logging():
    """إعداد نظام السجلات"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/crypto_forecasting.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

class DataScaler:
    """فئة لتطبيع البيانات"""
    
    def __init__(self):
        self.scalers = {}
    
    def fit_transform(self, data, columns=None):
        """تدريب وتطبيق التطبيع"""
        if columns is None:
            columns = data.columns
        
        scaled_data = data.copy()
        
        for column in columns:
            scaler = MinMaxScaler()
            scaled_data[column] = scaler.fit_transform(data[[column]])
            self.scalers[column] = scaler
        
        return scaled_data
    
    def transform(self, data, columns=None):
        """تطبيق التطبيع على بيانات جديدة"""
        if columns is None:
            columns = data.columns
        
        scaled_data = data.copy()
        
        for column in columns:
            if column in self.scalers:
                scaled_data[column] = self.scalers[column].transform(data[[column]])
        
        return scaled_data
    
    def inverse_transform(self, data, column):
        """عكس التطبيع"""
        if column in self.scalers:
            return self.scalers[column].inverse_transform(data.reshape(-1, 1))
        return data
    
    def save(self, filepath):
        """حفظ المطبعات"""
        joblib.dump(self.scalers, filepath)
    
    def load(self, filepath):
        """تحميل المطبعات"""
        self.scalers = joblib.load(filepath)

def create_sequences(data, sequence_length, target_column='close'):
    """إنشاء تسلسلات زمنية للتدريب"""
    X, y = [], []
    
    for i in range(sequence_length, len(data)):
        X.append(data.iloc[i-sequence_length:i].values)
        y.append(data[target_column].iloc[i])
    
    return np.array(X), np.array(y)

def create_multi_horizon_sequences(data, sequence_length, horizons, target_column='close'):
    """إنشاء تسلسلات متعددة الآفاق"""
    X, y = [], []
    
    max_horizon = max(horizons)
    
    for i in range(sequence_length, len(data) - max_horizon):
        X.append(data.iloc[i-sequence_length:i].values)
        
        # إنشاء أهداف متعددة الآفاق
        targets = []
        for horizon in horizons:
            if i + horizon < len(data):
                targets.append(data[target_column].iloc[i + horizon])
            else:
                targets.append(data[target_column].iloc[-1])  # استخدام آخر قيمة
        
        y.append(targets)
    
    return np.array(X), np.array(y)

def calculate_metrics(y_true, y_pred):
    """حساب مقاييس الأداء"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    # حساب MAPE (Mean Absolute Percentage Error)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'R2': r2,
        'MAPE': mape
    }

def save_predictions(predictions, symbol, timestamp=None):
    """حفظ التنبؤات"""
    if timestamp is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    filename = f'data/{symbol}_predictions_{timestamp}.json'
    
    with open(filename, 'w') as f:
        json.dump(predictions, f, indent=2, default=str)
    
    logger.info(f"تم حفظ التنبؤات في: {filename}")

def load_predictions(symbol, timestamp=None):
    """تحميل التنبؤات"""
    if timestamp is None:
        # البحث عن أحدث ملف
        files = [f for f in os.listdir('data') if f.startswith(f'{symbol}_predictions_')]
        if not files:
            return None
        filename = f'data/{sorted(files)[-1]}'
    else:
        filename = f'data/{symbol}_predictions_{timestamp}.json'
    
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"لم يتم العثور على ملف التنبؤات: {filename}")
        return None

def format_currency(value, currency='USD'):
    """تنسيق العملة"""
    if currency == 'USD':
        return f"${value:,.2f}"
    return f"{value:,.2f} {currency}"

def calculate_percentage_change(old_value, new_value):
    """حساب نسبة التغيير"""
    if old_value == 0:
        return 0
    return ((new_value - old_value) / old_value) * 100

def get_trend_direction(current_price, predicted_price):
    """تحديد اتجاه الترند"""
    change = calculate_percentage_change(current_price, predicted_price)
    
    if change > 2:
        return "صاعد قوي", "🚀"
    elif change > 0.5:
        return "صاعد", "📈"
    elif change > -0.5:
        return "مستقر", "➡️"
    elif change > -2:
        return "هابط", "📉"
    else:
        return "هابط قوي", "🔻"

def validate_data(data, required_columns):
    """التحقق من صحة البيانات"""
    missing_columns = [col for col in required_columns if col not in data.columns]
    
    if missing_columns:
        raise ValueError(f"الأعمدة المفقودة: {missing_columns}")
    
    # التحقق من القيم المفقودة
    null_counts = data[required_columns].isnull().sum()
    if null_counts.sum() > 0:
        logger.warning(f"القيم المفقودة: {null_counts.to_dict()}")
    
    return True

def clean_data(data):
    """تنظيف البيانات"""
    # إزالة القيم المكررة
    data = data.drop_duplicates()
    
    # ملء القيم المفقودة
    data = data.fillna(method='ffill').fillna(method='bfill')
    
    # إزالة القيم الشاذة (Outliers)
    numeric_columns = data.select_dtypes(include=[np.number]).columns
    
    for column in numeric_columns:
        Q1 = data[column].quantile(0.25)
        Q3 = data[column].quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        # استبدال القيم الشاذة بالحدود
        data[column] = data[column].clip(lower=lower_bound, upper=upper_bound)
    
    return data

def get_market_status():
    """الحصول على حالة السوق"""
    now = datetime.now()
    
    # السوق مفتوح 24/7 للعملات المشفرة
    return {
        'is_open': True,
        'status': 'مفتوح',
        'next_update': now + timedelta(hours=1)
    }
