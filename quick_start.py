#!/usr/bin/env python3
"""
تشغيل سريع للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Quick Start for Advanced Cryptocurrency Price Forecasting System
"""

import os
import sys
import time
import webbrowser
from datetime import datetime

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🚀 النظام المتقدم للتنبؤ بأسعار العملات المشفرة 🚀        ║
    ║         Advanced Cryptocurrency Price Forecasting            ║
    ║                                                              ║
    ║                    🔗 التشغيل السريع 🔗                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_basic_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 التحقق من المتطلبات الأساسية...")
    
    # التحقق من Python
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    
    # التحقق من المكتبات الأساسية
    basic_packages = ['flask', 'pandas', 'numpy']
    missing = []
    
    for package in basic_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - مفقود")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing)}")
        print("💡 تشغيل: pip install flask pandas numpy")
        return False
    
    return True

def create_minimal_app():
    """إنشاء تطبيق مبسط للاختبار"""
    
    app_code = '''
from flask import Flask, render_template, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
from datetime import datetime
import os

app = Flask(__name__)
CORS(app)

# Mock data for testing
CRYPTO_SYMBOLS = ['BTC-USD', 'ETH-USD', 'ADA-USD']

@app.route('/')
def index():
    return render_template('dashboard.html')

@app.route('/predictions')
def predictions():
    return render_template('predictions.html')

@app.route('/analysis')
def analysis():
    return render_template('analysis.html')

@app.route('/models')
def models_page():
    return render_template('models.html')

@app.route('/api/symbols')
def get_symbols():
    return jsonify({
        'symbols': CRYPTO_SYMBOLS,
        'default': 'BTC-USD'
    })

@app.route('/api/status')
def get_status():
    return jsonify({
        'system_status': 'نشط',
        'models_initialized': len(CRYPTO_SYMBOLS),
        'last_update': datetime.now().isoformat()
    })

@app.route('/api/data/<symbol>')
def get_data(symbol):
    # Mock historical data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    prices = 50000 + np.cumsum(np.random.randn(100) * 100)
    
    historical_data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        historical_data.append({
            'date': date.isoformat(),
            'open': price + np.random.randn() * 50,
            'high': price + abs(np.random.randn() * 100),
            'low': price - abs(np.random.randn() * 100),
            'close': price,
            'volume': np.random.randint(1000000, 10000000)
        })
    
    return jsonify({
        'symbol': symbol,
        'historical_data': historical_data[-30:],  # Last 30 days
        'real_time_data': {
            'current_price': float(prices[-1]),
            'previous_close': float(prices[-2]),
            'day_high': float(prices[-1] + 500),
            'day_low': float(prices[-1] - 500),
            'volume': int(np.random.randint(5000000, 15000000))
        },
        'sentiment_score': np.random.uniform(-0.5, 0.5),
        'last_updated': datetime.now().isoformat()
    })

@app.route('/api/predict/<symbol>')
def get_predictions(symbol):
    current_price = 50000 + np.random.randn() * 5000
    
    predictions = {
        'symbol': symbol,
        'current_price': current_price,
        'formatted_current_price': f"${current_price:,.2f}",
        'predictions': {
            'lstm': {},
            'ensemble': {}
        },
        'timestamp': datetime.now().isoformat()
    }
    
    # Mock predictions
    for horizon in [1, 3, 7, 30]:
        change = np.random.uniform(-0.05, 0.05)
        pred_price = current_price * (1 + change)
        
        predictions['predictions']['lstm'][f'{horizon}d'] = {
            'predicted_price': pred_price,
            'formatted_price': f"${pred_price:,.2f}",
            'change_percentage': change * 100,
            'trend': 'صاعد' if change > 0 else 'هابط',
            'emoji': '📈' if change > 0 else '📉'
        }
    
    return jsonify(predictions)

if __name__ == '__main__':
    print("🌐 تشغيل الخادم...")
    print("🔗 الروابط المتاحة:")
    print("   http://localhost:5000/ - الصفحة الرئيسية")
    print("   http://localhost:5000/predictions - التنبؤات")
    print("   http://localhost:5000/analysis - التحليل")
    print("   http://localhost:5000/models - النماذج")
    print("\\n⏹️ اضغط Ctrl+C لإيقاف الخادم")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
'''
    
    with open('minimal_app.py', 'w', encoding='utf-8') as f:
        f.write(app_code)
    
    print("✅ تم إنشاء التطبيق المبسط: minimal_app.py")

def main():
    """الدالة الرئيسية"""
    print_banner()
    print(f"🕐 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # التحقق من المتطلبات
    if not check_basic_requirements():
        print("\n💡 لتثبيت المتطلبات الأساسية:")
        print("   pip install flask pandas numpy flask-cors")
        print("\n💡 لتثبيت جميع المتطلبات:")
        print("   pip install -r requirements.txt")
        return
    
    print("\n🎯 اختر طريقة التشغيل:")
    print("1. تشغيل النظام الكامل (app.py)")
    print("2. تشغيل النسخة المبسطة للاختبار")
    print("3. إنشاء تطبيق مبسط فقط")
    
    try:
        choice = input("\nاختر (1-3): ").strip()
        
        if choice == '1':
            print("\n🚀 تشغيل النظام الكامل...")
            if os.path.exists('app.py'):
                os.system('python app.py')
            else:
                print("❌ ملف app.py غير موجود")
        
        elif choice == '2':
            print("\n🔧 إنشاء وتشغيل النسخة المبسطة...")
            create_minimal_app()
            
            print("\n🌐 تشغيل الخادم...")
            time.sleep(2)
            
            # فتح المتصفح
            try:
                webbrowser.open('http://localhost:5000')
            except:
                pass
            
            os.system('python minimal_app.py')
        
        elif choice == '3':
            print("\n🔧 إنشاء التطبيق المبسط...")
            create_minimal_app()
            print("✅ تم إنشاء minimal_app.py")
            print("💡 لتشغيله: python minimal_app.py")
        
        else:
            print("❌ اختيار غير صحيح")
    
    except KeyboardInterrupt:
        print("\n👋 تم الإلغاء بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")

if __name__ == "__main__":
    main()
