"""
نموذج التجميع المتقدم للتنبؤ بأسعار العملات المشفرة
Advanced Ensemble Model for Cryptocurrency Price Forecasting
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
import xgboost as xgb
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
import joblib
import warnings
warnings.filterwarnings('ignore')

from config import Config, ModelConfig
from utils import logger, calculate_metrics
from .lstm_model import LSTMPredictor

class EnsemblePredictor:
    """نموذج التجميع المتقدم"""
    
    def __init__(self):
        self.logger = logger
        self.models = {}
        self.weights = {}
        self.is_trained = False
        
        # تهيئة النماذج
        self._initialize_models()
    
    def _initialize_models(self):
        """تهيئة النماذج المختلفة"""
        try:
            # نموذج LSTM
            self.models['lstm'] = LSTMPredictor()
            
            # نموذج XGBoost
            self.models['xgboost'] = xgb.XGBRegressor(**ModelConfig.XGBOOST_CONFIG)
            
            # نموذج Random Forest
            self.models['random_forest'] = RandomForestRegressor(**ModelConfig.RF_CONFIG)
            
            # نموذج Gradient Boosting
            self.models['gradient_boosting'] = GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
            
            # نموذج Linear Regression للتجميع
            self.models['meta_learner'] = LinearRegression()
            
            self.logger.info("تم تهيئة جميع النماذج")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة النماذج: {e}")
    
    def prepare_lstm_data(self, data, sequence_length=60):
        """تحضير البيانات لنموذج LSTM"""
        try:
            X_lstm = []
            y_lstm = []
            
            for i in range(sequence_length, len(data)):
                X_lstm.append(data.iloc[i-sequence_length:i].values)
                y_lstm.append(data['close'].iloc[i])
            
            return np.array(X_lstm), np.array(y_lstm)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحضير بيانات LSTM: {e}")
            return None, None
    
    def prepare_ml_data(self, data, sequence_length=60):
        """تحضير البيانات للنماذج التقليدية"""
        try:
            X_ml = []
            y_ml = []
            
            for i in range(sequence_length, len(data)):
                # تحويل التسلسل الزمني إلى خصائص مسطحة
                sequence = data.iloc[i-sequence_length:i].values.flatten()
                X_ml.append(sequence)
                y_ml.append(data['close'].iloc[i])
            
            return np.array(X_ml), np.array(y_ml)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحضير بيانات ML: {e}")
            return None, None
    
    def fit_arima(self, data, order=None):
        """تدريب نموذج ARIMA"""
        try:
            if order is None:
                order = ModelConfig.ARIMA_CONFIG['order']
            
            # استخدام بيانات السعر فقط
            price_data = data['close'].dropna()
            
            # تدريب النموذج
            arima_model = ARIMA(price_data, order=order)
            fitted_model = arima_model.fit()
            
            self.models['arima'] = fitted_model
            self.logger.info("تم تدريب نموذج ARIMA")
            
            return fitted_model
            
        except Exception as e:
            self.logger.error(f"خطأ في تدريب ARIMA: {e}")
            return None
    
    def train(self, data, test_size=0.2, sequence_length=60):
        """تدريب جميع النماذج"""
        try:
            self.logger.info("بدء تدريب نموذج التجميع...")
            
            # تقسيم البيانات
            split_idx = int(len(data) * (1 - test_size))
            train_data = data[:split_idx]
            test_data = data[split_idx:]
            
            # تحضير البيانات
            X_lstm_train, y_lstm_train = self.prepare_lstm_data(train_data, sequence_length)
            X_ml_train, y_ml_train = self.prepare_ml_data(train_data, sequence_length)
            
            X_lstm_test, y_lstm_test = self.prepare_lstm_data(test_data, sequence_length)
            X_ml_test, y_ml_test = self.prepare_ml_data(test_data, sequence_length)
            
            # تدريب النماذج الفردية
            predictions_train = {}
            predictions_test = {}
            
            # 1. تدريب LSTM
            self.logger.info("تدريب نموذج LSTM...")
            lstm_model = self.models['lstm']
            lstm_model.n_features = X_lstm_train.shape[2]
            lstm_model.build_model('standard')
            lstm_model.train(X_lstm_train, y_lstm_train)
            
            predictions_train['lstm'] = lstm_model.predict(X_lstm_train)
            predictions_test['lstm'] = lstm_model.predict(X_lstm_test)
            
            # 2. تدريب XGBoost
            self.logger.info("تدريب نموذج XGBoost...")
            self.models['xgboost'].fit(X_ml_train, y_ml_train)
            predictions_train['xgboost'] = self.models['xgboost'].predict(X_ml_train)
            predictions_test['xgboost'] = self.models['xgboost'].predict(X_ml_test)
            
            # 3. تدريب Random Forest
            self.logger.info("تدريب نموذج Random Forest...")
            self.models['random_forest'].fit(X_ml_train, y_ml_train)
            predictions_train['random_forest'] = self.models['random_forest'].predict(X_ml_train)
            predictions_test['random_forest'] = self.models['random_forest'].predict(X_ml_test)
            
            # 4. تدريب Gradient Boosting
            self.logger.info("تدريب نموذج Gradient Boosting...")
            self.models['gradient_boosting'].fit(X_ml_train, y_ml_train)
            predictions_train['gradient_boosting'] = self.models['gradient_boosting'].predict(X_ml_train)
            predictions_test['gradient_boosting'] = self.models['gradient_boosting'].predict(X_ml_test)
            
            # 5. تدريب ARIMA
            self.logger.info("تدريب نموذج ARIMA...")
            arima_model = self.fit_arima(train_data)
            if arima_model:
                # التنبؤ للبيانات التدريبية والاختبارية
                arima_train_pred = arima_model.fittedvalues[-len(y_ml_train):]
                arima_test_pred = arima_model.forecast(steps=len(y_ml_test))
                
                predictions_train['arima'] = arima_train_pred
                predictions_test['arima'] = arima_test_pred
            
            # 6. تدريب Meta-Learner (Stacking)
            self.logger.info("تدريب Meta-Learner...")
            
            # إنشاء مصفوفة التنبؤات للتدريب
            train_meta_features = np.column_stack([
                predictions_train[model] for model in predictions_train.keys()
            ])
            
            # تدريب Meta-Learner
            self.models['meta_learner'].fit(train_meta_features, y_ml_train)
            
            # حساب الأوزان بناءً على الأداء
            self._calculate_weights(predictions_test, y_ml_test)
            
            self.is_trained = True
            self.logger.info("تم الانتهاء من تدريب نموذج التجميع")
            
            # تقييم الأداء
            return self._evaluate_ensemble(predictions_test, y_ml_test)
            
        except Exception as e:
            self.logger.error(f"خطأ في تدريب نموذج التجميع: {e}")
            return None
    
    def _calculate_weights(self, predictions, y_true):
        """حساب أوزان النماذج بناءً على الأداء"""
        try:
            errors = {}
            
            for model_name, pred in predictions.items():
                mse = mean_squared_error(y_true, pred)
                errors[model_name] = mse
            
            # حساب الأوزان (عكس الخطأ)
            total_inverse_error = sum(1/error for error in errors.values())
            
            for model_name, error in errors.items():
                self.weights[model_name] = (1/error) / total_inverse_error
            
            self.logger.info(f"أوزان النماذج: {self.weights}")
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب الأوزان: {e}")
    
    def predict(self, data, method='weighted_average'):
        """التنبؤ باستخدام نموذج التجميع"""
        try:
            if not self.is_trained:
                raise ValueError("النموذج غير مدرب")
            
            predictions = {}
            
            # الحصول على تنبؤات من جميع النماذج
            sequence_length = Config.SEQUENCE_LENGTH
            
            # تحضير البيانات
            X_lstm, _ = self.prepare_lstm_data(data, sequence_length)
            X_ml, _ = self.prepare_ml_data(data, sequence_length)
            
            if X_lstm is not None and len(X_lstm) > 0:
                predictions['lstm'] = self.models['lstm'].predict(X_lstm)
            
            if X_ml is not None and len(X_ml) > 0:
                predictions['xgboost'] = self.models['xgboost'].predict(X_ml)
                predictions['random_forest'] = self.models['random_forest'].predict(X_ml)
                predictions['gradient_boosting'] = self.models['gradient_boosting'].predict(X_ml)
            
            # ARIMA prediction
            if 'arima' in self.models and self.models['arima']:
                arima_pred = self.models['arima'].forecast(steps=len(X_ml))
                predictions['arima'] = arima_pred
            
            # دمج التنبؤات
            if method == 'weighted_average':
                return self._weighted_average_prediction(predictions)
            elif method == 'stacking':
                return self._stacking_prediction(predictions)
            else:
                return self._simple_average_prediction(predictions)
            
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ: {e}")
            return None
    
    def _weighted_average_prediction(self, predictions):
        """متوسط مرجح للتنبؤات"""
        try:
            if not predictions:
                return None
            
            # التأكد من أن جميع التنبؤات لها نفس الطول
            min_length = min(len(pred) for pred in predictions.values())
            
            weighted_pred = np.zeros(min_length)
            total_weight = 0
            
            for model_name, pred in predictions.items():
                if model_name in self.weights:
                    weight = self.weights[model_name]
                    weighted_pred += weight * pred[:min_length]
                    total_weight += weight
            
            if total_weight > 0:
                weighted_pred /= total_weight
            
            return weighted_pred
            
        except Exception as e:
            self.logger.error(f"خطأ في المتوسط المرجح: {e}")
            return None
    
    def _stacking_prediction(self, predictions):
        """تنبؤ باستخدام Stacking"""
        try:
            if not predictions or 'meta_learner' not in self.models:
                return self._simple_average_prediction(predictions)
            
            # إنشاء مصفوفة التنبؤات
            min_length = min(len(pred) for pred in predictions.values())
            meta_features = np.column_stack([
                pred[:min_length] for pred in predictions.values()
            ])
            
            # التنبؤ باستخدام Meta-Learner
            final_prediction = self.models['meta_learner'].predict(meta_features)
            
            return final_prediction
            
        except Exception as e:
            self.logger.error(f"خطأ في Stacking: {e}")
            return self._simple_average_prediction(predictions)
    
    def _simple_average_prediction(self, predictions):
        """متوسط بسيط للتنبؤات"""
        try:
            if not predictions:
                return None
            
            min_length = min(len(pred) for pred in predictions.values())
            avg_pred = np.mean([pred[:min_length] for pred in predictions.values()], axis=0)
            
            return avg_pred
            
        except Exception as e:
            self.logger.error(f"خطأ في المتوسط البسيط: {e}")
            return None
    
    def _evaluate_ensemble(self, predictions, y_true):
        """تقييم أداء نموذج التجميع"""
        try:
            results = {}
            
            # تقييم كل نموذج فردي
            for model_name, pred in predictions.items():
                metrics = calculate_metrics(y_true, pred)
                results[model_name] = metrics
            
            # تقييم النموذج المجمع
            ensemble_pred = self._weighted_average_prediction(predictions)
            if ensemble_pred is not None:
                ensemble_metrics = calculate_metrics(y_true, ensemble_pred)
                results['ensemble'] = ensemble_metrics
            
            return results
            
        except Exception as e:
            self.logger.error(f"خطأ في التقييم: {e}")
            return None
    
    def save_models(self, base_path):
        """حفظ جميع النماذج"""
        try:
            import os
            os.makedirs(base_path, exist_ok=True)
            
            # حفظ LSTM
            if 'lstm' in self.models:
                lstm_path = os.path.join(base_path, 'lstm_model.h5')
                self.models['lstm'].save_model(lstm_path)
            
            # حفظ النماذج الأخرى
            for model_name, model in self.models.items():
                if model_name not in ['lstm', 'arima']:
                    model_path = os.path.join(base_path, f'{model_name}_model.pkl')
                    joblib.dump(model, model_path)
            
            # حفظ الأوزان
            weights_path = os.path.join(base_path, 'ensemble_weights.pkl')
            joblib.dump(self.weights, weights_path)
            
            self.logger.info(f"تم حفظ جميع النماذج في: {base_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ النماذج: {e}")
            return False

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء نموذج تجريبي
    ensemble = EnsemblePredictor()
    print("تم إنشاء نموذج التجميع بنجاح")
