/* تنسيقات لوحة التحكم للنظام المتقدم للتنبؤ بأسعار العملات المشفرة */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f1f5f9;
    color: var(--dark-color);
}

/* تنسيقات البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border: none;
    padding: 1rem 1.5rem;
}

.card-title {
    font-weight: 600;
    margin: 0;
}

/* بطاقات الأسعار */
.price-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.price-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.price-card.positive {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.price-card.negative {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
}

.price-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.price-change {
    font-size: 1.1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.price-symbol {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* مقياس المشاعر */
.sentiment-meter {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto;
    background: conic-gradient(
        from 0deg,
        var(--danger-color) 0deg 120deg,
        var(--warning-color) 120deg 240deg,
        var(--success-color) 240deg 360deg
    );
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sentiment-meter::before {
    content: '';
    position: absolute;
    width: 160px;
    height: 160px;
    background: white;
    border-radius: 50%;
}

.sentiment-value {
    position: relative;
    z-index: 2;
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
}

.sentiment-label {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.9rem;
    color: var(--secondary-color);
    white-space: nowrap;
}

/* تنسيقات التنبؤات */
.prediction-item {
    background: var(--light-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border-left: 4px solid var(--primary-color);
    transition: all 0.2s ease;
}

.prediction-item:hover {
    background: #e2e8f0;
    transform: translateX(-5px);
}

.prediction-horizon {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.prediction-price {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.prediction-change {
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.prediction-change.positive {
    color: var(--success-color);
}

.prediction-change.negative {
    color: var(--danger-color);
}

/* حالة النماذج */
.model-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.model-status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.model-status-badge.trained {
    background: var(--success-color);
    color: white;
}

.model-status-badge.not-trained {
    background: var(--secondary-color);
    color: white;
}

/* أزرار مخصصة */
.btn-gradient {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #1d4ed8, var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    color: white;
}

/* شريط التقدم المخصص */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e2e8f0;
}

.progress-bar {
    border-radius: 4px;
    background: linear-gradient(90deg, var(--primary-color), #3b82f6);
}

/* تنسيقات الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* تنسيقات الاستجابة */
@media (max-width: 768px) {
    .price-value {
        font-size: 1.5rem;
    }
    
    .sentiment-meter {
        width: 150px;
        height: 150px;
    }
    
    .sentiment-meter::before {
        width: 120px;
        height: 120px;
    }
    
    .sentiment-value {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تنسيقات إضافية للنصوص العربية */
.rtl-text {
    direction: rtl;
    text-align: right;
}

.ltr-numbers {
    direction: ltr;
    display: inline-block;
}

/* تنسيقات الحالة */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 0.5rem;
}

.status-indicator.online {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.status-indicator.offline {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
}

/* تنسيقات الجداول */
.table-custom {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table-custom th {
    background: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
}

.table-custom td {
    border: none;
    border-bottom: 1px solid #e2e8f0;
    vertical-align: middle;
}

/* تأثيرات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* تنسيقات إضافية للتحسين */
.crypto-icon {
    width: 24px;
    height: 24px;
    margin-left: 0.5rem;
}

.trend-arrow {
    font-size: 1.2rem;
    margin-left: 0.25rem;
}

.data-timestamp {
    font-size: 0.8rem;
    color: var(--secondary-color);
    font-style: italic;
}

/* تحسينات شريط التنقل */
.navbar-nav .nav-link {
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 0.25rem;
    padding: 0.5rem 1rem !important;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.navbar-nav .nav-link i {
    margin-left: 0.5rem;
}

/* تأثيرات التحميل المحسنة */
.page-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
