@echo off
chcp 65001 >nul
title نظام التنبؤ بالعملات المشفرة - Crypto Prediction System

echo.
echo ========================================
echo    🚀 نظام التنبؤ بالعملات المشفرة
echo    Advanced Crypto Prediction System
echo ========================================
echo.

echo 📋 جاري فحص المتطلبات...
echo Checking requirements...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً
    echo ❌ Python is not installed! Please install Python first
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo ✅ Python is available

echo.
echo 📦 جاري تثبيت المكتبات المطلوبة...
echo Installing required packages...

pip install flask flask-cors numpy pandas >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: قد تكون بعض المكتبات مثبتة مسبقاً
    echo ⚠️ Warning: Some packages might already be installed
)

echo ✅ تم تثبيت المكتبات
echo ✅ Packages installed

echo.
echo 🌐 جاري تشغيل الخادم...
echo Starting server...
echo.
echo 🔗 الروابط المتاحة / Available URLs:
echo    http://localhost:5000 - الصفحة الرئيسية / Main Page
echo    http://127.0.0.1:5000 - رابط بديل / Alternative URL
echo.
echo ⚠️ ملاحظة مهمة: استخدم http:// وليس https://
echo ⚠️ Important: Use http:// not https://
echo.
echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
echo ⏹️ Press Ctrl+C to stop the server
echo.

python simple_server.py

echo.
echo 👋 تم إيقاف الخادم
echo 👋 Server stopped
pause
