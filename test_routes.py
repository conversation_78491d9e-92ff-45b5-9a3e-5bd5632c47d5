#!/usr/bin/env python3
"""
اختبار روابط النظام المتقدم للتنبؤ بأسعار العملات المشفرة
Routes Testing for Advanced Cryptocurrency Price Forecasting System
"""

import requests
import sys
from datetime import datetime

def test_routes():
    """اختبار جميع روابط النظام"""
    
    base_url = "http://localhost:5000"
    
    # قائمة الروابط للاختبار
    routes = {
        'الصفحة الرئيسية': '/',
        'لوحة التحكم': '/dashboard',
        'صفحة التنبؤات': '/predictions',
        'صفحة التحليل': '/analysis',
        'صفحة النماذج': '/models',
        'API - قائمة العملات': '/api/symbols',
        'API - حالة النظام': '/api/status',
        'API - بيانات البيتكوين': '/api/data/BTC-USD',
        'API - تنبؤات البيتكوين': '/api/predict/BTC-USD',
        'API - تحليل البيتكوين': '/api/analysis/BTC-USD',
        'API - مقارنة العملات': '/api/compare'
    }
    
    print("🧪 اختبار روابط النظام...")
    print("=" * 60)
    
    results = {}
    
    for name, route in routes.items():
        url = base_url + route
        
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                status = "✅ يعمل"
                color = "\033[92m"  # أخضر
            elif response.status_code == 404:
                status = "❌ غير موجود"
                color = "\033[91m"  # أحمر
            elif response.status_code == 500:
                status = "⚠️ خطأ في الخادم"
                color = "\033[93m"  # أصفر
            else:
                status = f"⚠️ كود {response.status_code}"
                color = "\033[93m"  # أصفر
            
            results[name] = {
                'status': response.status_code,
                'working': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
            
            print(f"{color}{name:25} {status:15} ({response.elapsed.total_seconds():.2f}s)\033[0m")
            
        except requests.exceptions.ConnectionError:
            print(f"\033[91m{name:25} ❌ لا يمكن الاتصال\033[0m")
            results[name] = {'status': 'connection_error', 'working': False}
            
        except requests.exceptions.Timeout:
            print(f"\033[93m{name:25} ⏰ انتهت المهلة الزمنية\033[0m")
            results[name] = {'status': 'timeout', 'working': False}
            
        except Exception as e:
            print(f"\033[91m{name:25} ❌ خطأ: {str(e)[:30]}\033[0m")
            results[name] = {'status': 'error', 'working': False}
    
    print("=" * 60)
    
    # إحصائيات النتائج
    working_count = sum(1 for r in results.values() if r.get('working', False))
    total_count = len(results)
    
    print(f"\n📊 النتائج:")
    print(f"   ✅ الروابط العاملة: {working_count}/{total_count}")
    print(f"   ❌ الروابط المعطلة: {total_count - working_count}/{total_count}")
    print(f"   📈 نسبة النجاح: {(working_count/total_count)*100:.1f}%")
    
    if working_count == total_count:
        print(f"\n🎉 ممتاز! جميع الروابط تعمل بشكل صحيح")
        return True
    else:
        print(f"\n⚠️ بعض الروابط لا تعمل. يرجى التحقق من الخادم.")
        return False

def test_navigation_flow():
    """اختبار تدفق التنقل بين الصفحات"""
    
    print("\n🔄 اختبار تدفق التنقل...")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    # تسلسل التنقل المتوقع
    navigation_flow = [
        ('الصفحة الرئيسية', '/'),
        ('صفحة التنبؤات', '/predictions'),
        ('صفحة التحليل', '/analysis'),
        ('صفحة النماذج', '/models'),
        ('العودة للرئيسية', '/')
    ]
    
    for step, (name, route) in enumerate(navigation_flow, 1):
        try:
            url = base_url + route
            response = session.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ الخطوة {step}: {name} - نجح")
            else:
                print(f"❌ الخطوة {step}: {name} - فشل (كود {response.status_code})")
                return False
                
        except Exception as e:
            print(f"❌ الخطوة {step}: {name} - خطأ: {e}")
            return False
    
    print("🎉 تم اختبار تدفق التنقل بنجاح!")
    return True

def test_api_responses():
    """اختبار استجابات API"""
    
    print("\n🔌 اختبار استجابات API...")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    api_tests = [
        {
            'name': 'قائمة العملات',
            'url': '/api/symbols',
            'expected_keys': ['symbols', 'default']
        },
        {
            'name': 'حالة النظام',
            'url': '/api/status',
            'expected_keys': ['system_status', 'models_initialized']
        }
    ]
    
    for test in api_tests:
        try:
            url = base_url + test['url']
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # التحقق من وجود المفاتيح المتوقعة
                    missing_keys = [key for key in test['expected_keys'] if key not in data]
                    
                    if not missing_keys:
                        print(f"✅ {test['name']}: البيانات صحيحة")
                    else:
                        print(f"⚠️ {test['name']}: مفاتيح مفقودة: {missing_keys}")
                        
                except ValueError:
                    print(f"❌ {test['name']}: استجابة غير صالحة (ليست JSON)")
            else:
                print(f"❌ {test['name']}: كود الاستجابة {response.status_code}")
                
        except Exception as e:
            print(f"❌ {test['name']}: خطأ - {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار شامل لنظام التنبؤ بأسعار العملات المشفرة")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # اختبار الروابط الأساسية
        routes_ok = test_routes()
        
        if routes_ok:
            # اختبار تدفق التنقل
            navigation_ok = test_navigation_flow()
            
            # اختبار API
            test_api_responses()
            
            if navigation_ok:
                print(f"\n🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.")
                return 0
        
        print(f"\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه.")
        return 1
        
    except KeyboardInterrupt:
        print(f"\n👋 تم إيقاف الاختبار بواسطة المستخدم")
        return 1
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
