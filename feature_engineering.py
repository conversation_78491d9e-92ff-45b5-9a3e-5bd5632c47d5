"""
وحدة هندسة الخصائص للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Feature Engineering Module for Advanced Cryptocurrency Price Forecasting System
"""

import pandas as pd
import numpy as np
import ta
from ta.utils import dropna
from ta.volatility import BollingerBands
from ta.trend import MACD, EMAIndicator, SMAIndicator
from ta.momentum import RSIIndicator, StochasticOscillator
from ta.volume import OnBalanceVolumeIndicator, VolumeSMAIndicator
from sklearn.preprocessing import MinMaxScaler, StandardScaler
import logging
from config import Config
from utils import logger, DataScaler

class FeatureEngineer:
    """مهندس الخصائص المتقدم"""
    
    def __init__(self):
        self.logger = logger
        self.scaler = DataScaler()
        self.feature_columns = []
    
    def add_technical_indicators(self, data):
        """إضافة المؤشرات الفنية"""
        try:
            self.logger.info("إضافة المؤشرات الفنية...")
            
            # التأكد من وجود الأعمدة المطلوبة
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                raise ValueError(f"الأعمدة المفقودة: {missing_columns}")
            
            # نسخة من البيانات
            df = data.copy()
            
            # 1. مؤشر القوة النسبية (RSI)
            rsi_period = Config.TECHNICAL_INDICATORS['RSI']['period']
            rsi = RSIIndicator(close=df['close'], window=rsi_period)
            df[f'rsi_{rsi_period}'] = rsi.rsi()
            
            # 2. مؤشر MACD
            macd_config = Config.TECHNICAL_INDICATORS['MACD']
            macd = MACD(
                close=df['close'],
                window_fast=macd_config['fast'],
                window_slow=macd_config['slow'],
                window_sign=macd_config['signal']
            )
            df['macd'] = macd.macd()
            df['macd_signal'] = macd.macd_signal()
            df['macd_histogram'] = macd.macd_diff()
            
            # 3. المتوسطات المتحركة البسيطة (SMA)
            for period in Config.TECHNICAL_INDICATORS['SMA']['periods']:
                sma = SMAIndicator(close=df['close'], window=period)
                df[f'sma_{period}'] = sma.sma_indicator()
            
            # 4. المتوسطات المتحركة الأسية (EMA)
            for period in Config.TECHNICAL_INDICATORS['EMA']['periods']:
                ema = EMAIndicator(close=df['close'], window=period)
                df[f'ema_{period}'] = ema.ema_indicator()
            
            # 5. نطاقات بولينجر (Bollinger Bands)
            bb_config = Config.TECHNICAL_INDICATORS['BOLLINGER']
            bb = BollingerBands(
                close=df['close'],
                window=bb_config['period'],
                window_dev=bb_config['std']
            )
            df['bb_upper'] = bb.bollinger_hband()
            df['bb_middle'] = bb.bollinger_mavg()
            df['bb_lower'] = bb.bollinger_lband()
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # 6. مؤشر ستوكاستيك (Stochastic)
            stoch_config = Config.TECHNICAL_INDICATORS['STOCH']
            stoch = StochasticOscillator(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                window=stoch_config['k_period'],
                smooth_window=stoch_config['d_period']
            )
            df['stoch_k'] = stoch.stoch()
            df['stoch_d'] = stoch.stoch_signal()
            
            # 7. متوسط المدى الحقيقي (ATR)
            atr_period = Config.TECHNICAL_INDICATORS['ATR']['period']
            df['atr'] = ta.volatility.average_true_range(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                window=atr_period
            )
            
            # 8. مؤشر الحجم المتوازن (OBV)
            obv = OnBalanceVolumeIndicator(close=df['close'], volume=df['volume'])
            df['obv'] = obv.on_balance_volume()
            
            # 9. السعر المرجح بالحجم (VWAP)
            df['vwap'] = ta.volume.volume_weighted_average_price(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                volume=df['volume']
            )
            
            self.logger.info(f"تم إضافة {len(df.columns) - len(data.columns)} مؤشر فني")
            return df
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة المؤشرات الفنية: {e}")
            return data
    
    def add_price_features(self, data):
        """إضافة خصائص السعر المتقدمة"""
        try:
            df = data.copy()
            
            # 1. التغيرات السعرية
            df['price_change'] = df['close'].pct_change()
            df['price_change_abs'] = df['price_change'].abs()
            
            # 2. التقلبات (Volatility)
            for window in [5, 10, 20]:
                df[f'volatility_{window}'] = df['price_change'].rolling(window).std()
            
            # 3. العوائد اللوغاريتمية
            df['log_return'] = np.log(df['close'] / df['close'].shift(1))
            
            # 4. نسب الأسعار
            df['high_low_ratio'] = df['high'] / df['low']
            df['close_open_ratio'] = df['close'] / df['open']
            
            # 5. المديات السعرية
            df['daily_range'] = df['high'] - df['low']
            df['daily_range_pct'] = df['daily_range'] / df['close']
            
            # 6. الفجوات السعرية
            df['gap'] = df['open'] - df['close'].shift(1)
            df['gap_pct'] = df['gap'] / df['close'].shift(1)
            
            # 7. مستويات الدعم والمقاومة
            df['resistance_20'] = df['high'].rolling(20).max()
            df['support_20'] = df['low'].rolling(20).min()
            df['price_position'] = (df['close'] - df['support_20']) / (df['resistance_20'] - df['support_20'])
            
            return df
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة خصائص السعر: {e}")
            return data
    
    def add_volume_features(self, data):
        """إضافة خصائص الحجم"""
        try:
            df = data.copy()
            
            # 1. تغيرات الحجم
            df['volume_change'] = df['volume'].pct_change()
            df['volume_change_abs'] = df['volume_change'].abs()
            
            # 2. متوسطات الحجم
            for window in [5, 10, 20]:
                df[f'volume_sma_{window}'] = df['volume'].rolling(window).mean()
                df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
            
            # 3. مؤشر قوة الحجم
            df['volume_price_trend'] = ta.volume.volume_price_trend(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                volume=df['volume']
            )
            
            # 4. تدفق الأموال
            df['money_flow_index'] = ta.volume.money_flow_index(
                high=df['high'],
                low=df['low'],
                close=df['close'],
                volume=df['volume']
            )
            
            return df
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة خصائص الحجم: {e}")
            return data
    
    def add_time_features(self, data):
        """إضافة خصائص زمنية"""
        try:
            df = data.copy()
            
            # التأكد من أن الفهرس هو تاريخ
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # 1. خصائص التاريخ
            df['year'] = df.index.year
            df['month'] = df.index.month
            df['day'] = df.index.day
            df['day_of_week'] = df.index.dayofweek
            df['day_of_year'] = df.index.dayofyear
            
            # 2. خصائص دورية
            df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
            df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
            df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
            df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
            
            # 3. خصائص الربع
            df['quarter'] = df.index.quarter
            df['is_month_end'] = df.index.is_month_end.astype(int)
            df['is_quarter_end'] = df.index.is_quarter_end.astype(int)
            
            return df
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الخصائص الزمنية: {e}")
            return data
    
    def add_sentiment_features(self, data, sentiment_scores=None):
        """إضافة خصائص المشاعر"""
        try:
            df = data.copy()
            
            if sentiment_scores is not None:
                # إضافة درجات المشاعر
                if isinstance(sentiment_scores, (int, float)):
                    df['sentiment_score'] = sentiment_scores
                elif isinstance(sentiment_scores, dict):
                    # إذا كانت درجات المشاعر مرتبطة بالتواريخ
                    for date, score in sentiment_scores.items():
                        if date in df.index:
                            df.loc[date, 'sentiment_score'] = score
                
                # متوسطات متحركة للمشاعر
                if 'sentiment_score' in df.columns:
                    for window in [3, 7, 14]:
                        df[f'sentiment_sma_{window}'] = df['sentiment_score'].rolling(window).mean()
                        df[f'sentiment_std_{window}'] = df['sentiment_score'].rolling(window).std()
            else:
                # إضافة قيم افتراضية
                df['sentiment_score'] = 0.0
            
            return df
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة خصائص المشاعر: {e}")
            return data
    
    def create_features(self, data, sentiment_scores=None):
        """إنشاء جميع الخصائص"""
        try:
            self.logger.info("بدء إنشاء الخصائص...")
            
            # إضافة جميع أنواع الخصائص
            df = self.add_technical_indicators(data)
            df = self.add_price_features(df)
            df = self.add_volume_features(df)
            df = self.add_time_features(df)
            df = self.add_sentiment_features(df, sentiment_scores)
            
            # إزالة القيم المفقودة
            df = df.dropna()
            
            # حفظ أسماء الخصائص
            self.feature_columns = [col for col in df.columns if col not in ['open', 'high', 'low', 'close', 'volume']]
            
            self.logger.info(f"تم إنشاء {len(self.feature_columns)} خاصية")
            self.logger.info(f"عدد السجلات بعد التنظيف: {len(df)}")
            
            return df
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الخصائص: {e}")
            return data
    
    def scale_features(self, data, fit=True):
        """تطبيع الخصائص"""
        try:
            if fit:
                scaled_data = self.scaler.fit_transform(data, self.feature_columns + ['close'])
            else:
                scaled_data = self.scaler.transform(data, self.feature_columns + ['close'])
            
            return scaled_data
            
        except Exception as e:
            self.logger.error(f"خطأ في تطبيع الخصائص: {e}")
            return data
    
    def get_feature_importance(self, data, target='close'):
        """حساب أهمية الخصائص"""
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.feature_selection import mutual_info_regression
            
            X = data[self.feature_columns]
            y = data[target]
            
            # إزالة القيم المفقودة
            mask = ~(X.isnull().any(axis=1) | y.isnull())
            X_clean = X[mask]
            y_clean = y[mask]
            
            # Random Forest Feature Importance
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X_clean, y_clean)
            rf_importance = pd.Series(rf.feature_importances_, index=X_clean.columns)
            
            # Mutual Information
            mi_scores = mutual_info_regression(X_clean, y_clean)
            mi_importance = pd.Series(mi_scores, index=X_clean.columns)
            
            # دمج النتائج
            importance_df = pd.DataFrame({
                'random_forest': rf_importance,
                'mutual_info': mi_importance
            })
            
            importance_df['combined'] = (importance_df['random_forest'] + importance_df['mutual_info']) / 2
            importance_df = importance_df.sort_values('combined', ascending=False)
            
            return importance_df
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب أهمية الخصائص: {e}")
            return pd.DataFrame()

# مثال على الاستخدام
if __name__ == "__main__":
    from data_collector import CryptoDataCollector
    
    # جمع البيانات
    collector = CryptoDataCollector()
    data = collector.get_historical_data('BTC-USD')
    
    if not data.empty:
        # إنشاء الخصائص
        engineer = FeatureEngineer()
        featured_data = engineer.create_features(data)
        
        print(f"عدد الخصائص: {len(engineer.feature_columns)}")
        print(f"أسماء الخصائص: {engineer.feature_columns[:10]}...")  # أول 10 خصائص
        
        # حساب أهمية الخصائص
        importance = engineer.get_feature_importance(featured_data)
        print("\nأهم 10 خصائص:")
        print(importance.head(10))
