#!/usr/bin/env python3
"""
خادم مبسط للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Simple Server for Advanced Cryptocurrency Price Forecasting System
"""

from flask import Flask, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
from datetime import datetime
import json

app = Flask(__name__)
CORS(app)

# Mock data
CRYPTO_SYMBOLS = ['BTC-USD', 'ETH-USD', 'ADA-USD', 'DOT-USD', 'LINK-USD']

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام التنبؤ بالعملات المشفرة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .crypto-card { transition: transform 0.3s; }
            .crypto-card:hover { transform: translateY(-5px); }
            .price-up { color: #28a745; }
            .price-down { color: #dc3545; }
            .navbar-brand { font-weight: bold; }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-chart-line me-2"></i>
                    نظام التنبؤ بالعملات المشفرة
                </a>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predictions">
                            <i class="fas fa-crystal-ball me-1"></i>التنبؤات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">
                            <i class="fas fa-chart-bar me-1"></i>التحليل
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-rocket text-primary"></i>
                        مرحباً بك في نظام التنبؤ المتقدم
                    </h1>
                    <p class="lead">نظام ذكي للتنبؤ بأسعار العملات المشفرة باستخدام الذكاء الاصطناعي</p>
                </div>
            </div>

            <!-- Status Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-server fa-2x text-success mb-2"></i>
                            <h5 class="card-title">حالة النظام</h5>
                            <p class="card-text text-success">نشط</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                            <h5 class="card-title">العملات المدعومة</h5>
                            <p class="card-text">5 عملات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-brain fa-2x text-info mb-2"></i>
                            <h5 class="card-title">نماذج الذكاء الاصطناعي</h5>
                            <p class="card-text">LSTM + Ensemble</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                            <h5 class="card-title">آخر تحديث</h5>
                            <p class="card-text" id="lastUpdate">جاري التحميل...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Crypto Prices -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                أسعار العملات المشفرة المباشرة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="cryptoPrices" class="row">
                                <div class="col-12 text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mt-2">جاري تحميل البيانات...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <a href="/predictions" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-crystal-ball me-2"></i>
                                        عرض التنبؤات
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="/analysis" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-microscope me-2"></i>
                                        التحليل المتقدم
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <button class="btn btn-info btn-lg w-100" onclick="refreshData()">
                                        <i class="fas fa-sync-alt me-2"></i>
                                        تحديث البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // Update timestamp
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-SA');

            // Load crypto prices
            async function loadCryptoPrices() {
                try {
                    const response = await fetch('/api/prices');
                    const data = await response.json();
                    
                    const container = document.getElementById('cryptoPrices');
                    container.innerHTML = '';
                    
                    data.forEach(crypto => {
                        const changeClass = crypto.change >= 0 ? 'price-up' : 'price-down';
                        const changeIcon = crypto.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                        
                        container.innerHTML += `
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card crypto-card">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">${crypto.name}</h6>
                                        <h4 class="text-primary">$${crypto.price.toLocaleString()}</h4>
                                        <p class="card-text ${changeClass}">
                                            <i class="fas ${changeIcon}"></i>
                                            ${crypto.change.toFixed(2)}%
                                        </p>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } catch (error) {
                    console.error('Error loading prices:', error);
                    document.getElementById('cryptoPrices').innerHTML = 
                        '<div class="col-12 text-center text-danger">خطأ في تحميل البيانات</div>';
                }
            }

            function refreshData() {
                loadCryptoPrices();
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-SA');
            }

            // Load data on page load
            loadCryptoPrices();
            
            // Auto refresh every 30 seconds
            setInterval(refreshData, 30000);
        </script>
    </body>
    </html>
    """

@app.route('/predictions')
def predictions():
    """صفحة التنبؤات"""
    return """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>التنبؤات - نظام التنبؤ بالعملات المشفرة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-chart-line me-2"></i>نظام التنبؤ بالعملات المشفرة
                </a>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="/"><i class="fas fa-home me-1"></i>الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link active" href="/predictions"><i class="fas fa-crystal-ball me-1"></i>التنبؤات</a></li>
                    <li class="nav-item"><a class="nav-link" href="/analysis"><i class="fas fa-chart-bar me-1"></i>التحليل</a></li>
                </ul>
            </div>
        </nav>
        
        <div class="container mt-4">
            <h1><i class="fas fa-crystal-ball me-2"></i>التنبؤات المتقدمة</h1>
            <p class="lead">تنبؤات دقيقة لأسعار العملات المشفرة باستخدام نماذج LSTM</p>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                هذه الصفحة قيد التطوير. ستحتوي على تنبؤات مفصلة لجميع العملات المدعومة.
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/analysis')
def analysis():
    """صفحة التحليل"""
    return """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>التحليل - نظام التنبؤ بالعملات المشفرة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-chart-line me-2"></i>نظام التنبؤ بالعملات المشفرة
                </a>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="/"><i class="fas fa-home me-1"></i>الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link" href="/predictions"><i class="fas fa-crystal-ball me-1"></i>التنبؤات</a></li>
                    <li class="nav-item"><a class="nav-link active" href="/analysis"><i class="fas fa-chart-bar me-1"></i>التحليل</a></li>
                </ul>
            </div>
        </nav>
        
        <div class="container mt-4">
            <h1><i class="fas fa-microscope me-2"></i>التحليل المتقدم</h1>
            <p class="lead">تحليل فني شامل للعملات المشفرة</p>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                هذه الصفحة قيد التطوير. ستحتوي على تحليل فني مفصل وأدوات تحليل متقدمة.
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/api/prices')
def get_prices():
    """API لأسعار العملات"""
    prices = []
    for symbol in CRYPTO_SYMBOLS:
        base_price = np.random.uniform(20000, 70000) if 'BTC' in symbol else np.random.uniform(1000, 5000)
        change = np.random.uniform(-5, 5)
        
        prices.append({
            'symbol': symbol,
            'name': symbol.replace('-USD', ''),
            'price': base_price,
            'change': change
        })
    
    return jsonify(prices)

@app.route('/api/symbols')
def get_symbols():
    """API لقائمة العملات"""
    return jsonify({
        'symbols': CRYPTO_SYMBOLS,
        'default': 'BTC-USD'
    })

@app.route('/api/status')
def get_status():
    """API لحالة النظام"""
    return jsonify({
        'system_status': 'نشط',
        'models_initialized': len(CRYPTO_SYMBOLS),
        'last_update': datetime.now().isoformat(),
        'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

if __name__ == '__main__':
    print("🚀 النظام المبسط للتنبؤ بالعملات المشفرة")
    print("=" * 50)
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("🔗 الروابط المتاحة:")
    print("   http://localhost:5000/ - الصفحة الرئيسية")
    print("   http://localhost:5000/predictions - التنبؤات")
    print("   http://localhost:5000/analysis - التحليل")
    print("   http://localhost:5000/api/prices - أسعار العملات")
    print("   http://localhost:5000/api/status - حالة النظام")
    print("\n⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=False)
