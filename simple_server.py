#!/usr/bin/env python3
"""
خادم مبسط للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Simple Server for Advanced Cryptocurrency Price Forecasting System
"""

from flask import Flask, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
from datetime import datetime
import json

app = Flask(__name__)
CORS(app)

# Mock data
CRYPTO_SYMBOLS = ['BTC-USD', 'ETH-USD', 'ADA-USD', 'DOT-USD', 'LINK-USD']

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام التنبؤ بالعملات المشفرة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .crypto-card { transition: transform 0.3s; }
            .crypto-card:hover { transform: translateY(-5px); }
            .price-up { color: #28a745; }
            .price-down { color: #dc3545; }
            .navbar-brand { font-weight: bold; }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-chart-line me-2"></i>
                    نظام التنبؤ بالعملات المشفرة
                </a>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/predictions">
                            <i class="fas fa-crystal-ball me-1"></i>التنبؤات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis">
                            <i class="fas fa-chart-bar me-1"></i>التحليل
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-rocket text-primary"></i>
                        مرحباً بك في نظام التنبؤ المتقدم
                    </h1>
                    <p class="lead">نظام ذكي للتنبؤ بأسعار العملات المشفرة باستخدام الذكاء الاصطناعي</p>
                </div>
            </div>

            <!-- Status Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-server fa-2x text-success mb-2"></i>
                            <h5 class="card-title">حالة النظام</h5>
                            <p class="card-text text-success">نشط</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                            <h5 class="card-title">العملات المدعومة</h5>
                            <p class="card-text">5 عملات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-brain fa-2x text-info mb-2"></i>
                            <h5 class="card-title">نماذج الذكاء الاصطناعي</h5>
                            <p class="card-text">LSTM + Ensemble</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-center crypto-card">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                            <h5 class="card-title">آخر تحديث</h5>
                            <p class="card-text" id="lastUpdate">جاري التحميل...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Crypto Prices -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                أسعار العملات المشفرة المباشرة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="cryptoPrices" class="row">
                                <div class="col-12 text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mt-2">جاري تحميل البيانات...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <a href="/predictions" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-crystal-ball me-2"></i>
                                        عرض التنبؤات
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="/analysis" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-microscope me-2"></i>
                                        التحليل المتقدم
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <button class="btn btn-info btn-lg w-100" onclick="refreshData()">
                                        <i class="fas fa-sync-alt me-2"></i>
                                        تحديث البيانات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // Update timestamp
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-SA');

            // Load crypto prices
            async function loadCryptoPrices() {
                try {
                    const response = await fetch('/api/prices');
                    const data = await response.json();
                    
                    const container = document.getElementById('cryptoPrices');
                    container.innerHTML = '';
                    
                    data.forEach(crypto => {
                        const changeClass = crypto.change >= 0 ? 'price-up' : 'price-down';
                        const changeIcon = crypto.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                        
                        container.innerHTML += `
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card crypto-card">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">${crypto.name}</h6>
                                        <h4 class="text-primary">$${crypto.price.toLocaleString()}</h4>
                                        <p class="card-text ${changeClass}">
                                            <i class="fas ${changeIcon}"></i>
                                            ${crypto.change.toFixed(2)}%
                                        </p>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } catch (error) {
                    console.error('Error loading prices:', error);
                    document.getElementById('cryptoPrices').innerHTML = 
                        '<div class="col-12 text-center text-danger">خطأ في تحميل البيانات</div>';
                }
            }

            function refreshData() {
                loadCryptoPrices();
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('ar-SA');
            }

            // Load data on page load
            loadCryptoPrices();
            
            // Auto refresh every 30 seconds
            setInterval(refreshData, 30000);
        </script>
    </body>
    </html>
    """

@app.route('/predictions')
def predictions():
    """صفحة التنبؤات"""
    return """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>التنبؤات - نظام التنبؤ بالعملات المشفرة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            .prediction-card { transition: transform 0.3s; border-left: 4px solid #007bff; }
            .prediction-card:hover { transform: translateY(-3px); }
            .price-up { color: #28a745; }
            .price-down { color: #dc3545; }
            .confidence-high { color: #28a745; }
            .confidence-medium { color: #ffc107; }
            .confidence-low { color: #dc3545; }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-chart-line me-2"></i>نظام التنبؤ بالعملات المشفرة
                </a>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="/"><i class="fas fa-home me-1"></i>الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link active" href="/predictions"><i class="fas fa-crystal-ball me-1"></i>التنبؤات</a></li>
                    <li class="nav-item"><a class="nav-link" href="/analysis"><i class="fas fa-chart-bar me-1"></i>التحليل</a></li>
                </ul>
            </div>
        </nav>

        <div class="container mt-4">
            <div class="row mb-4">
                <div class="col-12">
                    <h1><i class="fas fa-crystal-ball me-2"></i>التنبؤات المتقدمة</h1>
                    <p class="lead">تنبؤات دقيقة لأسعار العملات المشفرة باستخدام نماذج LSTM</p>
                </div>
            </div>

            <!-- Prediction Controls -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-cog me-2"></i>إعدادات التنبؤ</h5>
                            <div class="mb-3">
                                <label class="form-label">اختر العملة:</label>
                                <select class="form-select" id="cryptoSelect">
                                    <option value="BTC-USD">Bitcoin (BTC)</option>
                                    <option value="ETH-USD">Ethereum (ETH)</option>
                                    <option value="ADA-USD">Cardano (ADA)</option>
                                    <option value="DOT-USD">Polkadot (DOT)</option>
                                    <option value="LINK-USD">Chainlink (LINK)</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">فترة التنبؤ:</label>
                                <select class="form-select" id="periodSelect">
                                    <option value="1">يوم واحد</option>
                                    <option value="7" selected>أسبوع واحد</option>
                                    <option value="30">شهر واحد</option>
                                </select>
                            </div>
                            <button class="btn btn-primary w-100" onclick="generatePrediction()">
                                <i class="fas fa-magic me-2"></i>إنشاء التنبؤ
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-info-circle me-2"></i>معلومات النموذج</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-brain text-primary me-2"></i><strong>النموذج:</strong> LSTM + Ensemble</li>
                                <li><i class="fas fa-chart-line text-success me-2"></i><strong>الدقة:</strong> 87.3%</li>
                                <li><i class="fas fa-database text-info me-2"></i><strong>البيانات:</strong> 2 سنة</li>
                                <li><i class="fas fa-clock text-warning me-2"></i><strong>آخر تدريب:</strong> اليوم</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Predictions Results -->
            <div id="predictionResults" style="display: none;">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card prediction-card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-area me-2"></i>
                                    نتائج التنبؤ - <span id="selectedCrypto">Bitcoin</span>
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 text-center">
                                        <h6>السعر الحالي</h6>
                                        <h4 class="text-primary" id="currentPrice">$45,230</h4>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <h6>السعر المتوقع</h6>
                                        <h4 class="price-up" id="predictedPrice">$47,850</h4>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <h6>التغيير المتوقع</h6>
                                        <h4 class="price-up" id="priceChange">+5.8%</h4>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <h6>مستوى الثقة</h6>
                                        <h4 class="confidence-high" id="confidence">85%</h4>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <canvas id="predictionChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Indicators -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>المؤشرات الفنية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 mb-3">
                                    <small class="text-muted">RSI (14)</small>
                                    <div class="d-flex justify-content-between">
                                        <span>65.4</span>
                                        <span class="badge bg-warning">محايد</span>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <small class="text-muted">MACD</small>
                                    <div class="d-flex justify-content-between">
                                        <span>+234.5</span>
                                        <span class="badge bg-success">صاعد</span>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <small class="text-muted">SMA (50)</small>
                                    <div class="d-flex justify-content-between">
                                        <span>$44,120</span>
                                        <span class="badge bg-success">فوق</span>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <small class="text-muted">Bollinger</small>
                                    <div class="d-flex justify-content-between">
                                        <span>وسط</span>
                                        <span class="badge bg-info">مستقر</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-newspaper me-2"></i>تحليل المشاعر
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>المشاعر الإيجابية</span>
                                    <span>68%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: 68%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>المشاعر المحايدة</span>
                                    <span>22%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" style="width: 22%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>المشاعر السلبية</span>
                                    <span>10%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-danger" style="width: 10%"></div>
                                </div>
                            </div>
                            <div class="text-center">
                                <span class="badge bg-success fs-6">إيجابي عام</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            let predictionChart = null;

            function generatePrediction() {
                const crypto = document.getElementById('cryptoSelect').value;
                const period = document.getElementById('periodSelect').value;

                // Show loading
                const btn = event.target;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحليل...';
                btn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    showPredictionResults(crypto, period);
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
            }

            function showPredictionResults(crypto, period) {
                // Generate mock data
                const currentPrice = Math.random() * 50000 + 20000;
                const change = (Math.random() - 0.5) * 20;
                const predictedPrice = currentPrice * (1 + change / 100);
                const confidence = Math.random() * 30 + 70;

                // Update UI
                document.getElementById('selectedCrypto').textContent = crypto.replace('-USD', '');
                document.getElementById('currentPrice').textContent = '$' + currentPrice.toLocaleString(undefined, {maximumFractionDigits: 0});
                document.getElementById('predictedPrice').textContent = '$' + predictedPrice.toLocaleString(undefined, {maximumFractionDigits: 0});
                document.getElementById('priceChange').textContent = (change >= 0 ? '+' : '') + change.toFixed(1) + '%';
                document.getElementById('confidence').textContent = confidence.toFixed(0) + '%';

                // Update colors
                const changeElement = document.getElementById('priceChange');
                const predictedElement = document.getElementById('predictedPrice');
                if (change >= 0) {
                    changeElement.className = 'price-up';
                    predictedElement.className = 'price-up';
                } else {
                    changeElement.className = 'price-down';
                    predictedElement.className = 'price-down';
                }

                // Show results
                document.getElementById('predictionResults').style.display = 'block';

                // Create chart
                createPredictionChart(currentPrice, predictedPrice, period);

                // Scroll to results
                document.getElementById('predictionResults').scrollIntoView({ behavior: 'smooth' });
            }

            function createPredictionChart(currentPrice, predictedPrice, period) {
                const ctx = document.getElementById('predictionChart').getContext('2d');

                if (predictionChart) {
                    predictionChart.destroy();
                }

                // Generate historical and predicted data
                const labels = [];
                const historicalData = [];
                const predictedData = [];

                // Historical data (last 30 days)
                for (let i = 30; i >= 1; i--) {
                    labels.push(`-${i}d`);
                    historicalData.push(currentPrice + (Math.random() - 0.5) * currentPrice * 0.1);
                    predictedData.push(null);
                }

                // Current price
                labels.push('اليوم');
                historicalData.push(currentPrice);
                predictedData.push(currentPrice);

                // Predicted data
                const days = parseInt(period);
                for (let i = 1; i <= days; i++) {
                    labels.push(`+${i}d`);
                    historicalData.push(null);
                    const progress = i / days;
                    predictedData.push(currentPrice + (predictedPrice - currentPrice) * progress);
                }

                predictionChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'السعر التاريخي',
                            data: historicalData,
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            fill: false,
                            tension: 0.4
                        }, {
                            label: 'التنبؤ',
                            data: predictedData,
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            borderDash: [5, 5],
                            fill: false,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'تنبؤ السعر'
                            },
                            legend: {
                                display: true
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: false,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });
            }
        </script>
    </body>
    </html>
    """

@app.route('/analysis')
def analysis():
    """صفحة التحليل"""
    return """
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>التحليل - نظام التنبؤ بالعملات المشفرة</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-chart-line me-2"></i>نظام التنبؤ بالعملات المشفرة
                </a>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="/"><i class="fas fa-home me-1"></i>الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link" href="/predictions"><i class="fas fa-crystal-ball me-1"></i>التنبؤات</a></li>
                    <li class="nav-item"><a class="nav-link active" href="/analysis"><i class="fas fa-chart-bar me-1"></i>التحليل</a></li>
                </ul>
            </div>
        </nav>
        
        <div class="container mt-4">
            <h1><i class="fas fa-microscope me-2"></i>التحليل المتقدم</h1>
            <p class="lead">تحليل فني شامل للعملات المشفرة</p>
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                هذه الصفحة قيد التطوير. ستحتوي على تحليل فني مفصل وأدوات تحليل متقدمة.
            </div>
        </div>
    </body>
    </html>
    """

@app.route('/api/prices')
def get_prices():
    """API لأسعار العملات"""
    prices = []
    for symbol in CRYPTO_SYMBOLS:
        base_price = np.random.uniform(20000, 70000) if 'BTC' in symbol else np.random.uniform(1000, 5000)
        change = np.random.uniform(-5, 5)
        
        prices.append({
            'symbol': symbol,
            'name': symbol.replace('-USD', ''),
            'price': base_price,
            'change': change
        })
    
    return jsonify(prices)

@app.route('/api/symbols')
def get_symbols():
    """API لقائمة العملات"""
    return jsonify({
        'symbols': CRYPTO_SYMBOLS,
        'default': 'BTC-USD'
    })

@app.route('/api/status')
def get_status():
    """API لحالة النظام"""
    return jsonify({
        'system_status': 'نشط',
        'models_initialized': len(CRYPTO_SYMBOLS),
        'last_update': datetime.now().isoformat(),
        'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

@app.errorhandler(400)
def bad_request(error):
    """معالج الأخطاء 400"""
    return jsonify({'error': 'Bad Request', 'message': 'طلب غير صحيح'}), 400

@app.errorhandler(404)
def not_found(error):
    """معالج الأخطاء 404"""
    return jsonify({'error': 'Not Found', 'message': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    """معالج الأخطاء 500"""
    return jsonify({'error': 'Internal Server Error', 'message': 'خطأ في الخادم'}), 500

if __name__ == '__main__':
    import logging

    # تقليل مستوى السجلات لتجنب رسائل الأخطاء المزعجة
    log = logging.getLogger('werkzeug')
    log.setLevel(logging.ERROR)

    print("🚀 النظام المبسط للتنبؤ بالعملات المشفرة")
    print("=" * 50)
    print("🌐 الخادم يعمل على: http://localhost:5000")
    print("🔗 الروابط المتاحة:")
    print("   http://localhost:5000/ - الصفحة الرئيسية")
    print("   http://localhost:5000/predictions - التنبؤات")
    print("   http://localhost:5000/analysis - التحليل")
    print("   http://localhost:5000/api/prices - أسعار العملات")
    print("   http://localhost:5000/api/status - حالة النظام")
    print("\n⚠️ ملاحظة: تأكد من استخدام http:// وليس https://")
    print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)

    try:
        app.run(host='127.0.0.1', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف الخادم بنجاح!")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
