/**
 * لوحة التحكم التفاعلية للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
 * Interactive Dashboard for Advanced Cryptocurrency Price Forecasting System
 */

class CryptoDashboard {
    constructor() {
        this.currentSymbol = 'BTC-USD';
        this.charts = {};
        this.updateInterval = null;
        this.isTraining = false;
        
        this.init();
    }
    
    async init() {
        console.log('تهيئة لوحة التحكم...');
        
        // تحميل قائمة العملات
        await this.loadSymbols();
        
        // تهيئة الأحداث
        this.setupEventListeners();
        
        // تحميل البيانات الأولية
        await this.loadInitialData();
        
        // بدء التحديث التلقائي
        this.startAutoUpdate();
        
        console.log('تم تهيئة لوحة التحكم بنجاح');
    }
    
    async loadSymbols() {
        try {
            const response = await fetch('/api/symbols');
            const data = await response.json();
            
            const symbolSelect = document.getElementById('symbolSelect');
            const trainSymbolSelect = document.getElementById('trainSymbolSelect');
            
            // مسح الخيارات الحالية
            symbolSelect.innerHTML = '<option value="">اختر العملة...</option>';
            trainSymbolSelect.innerHTML = '<option value="">اختر العملة للتدريب...</option>';
            
            // إضافة العملات
            data.symbols.forEach(symbol => {
                const option1 = new Option(this.getSymbolName(symbol), symbol);
                const option2 = new Option(this.getSymbolName(symbol), symbol);
                
                symbolSelect.add(option1);
                trainSymbolSelect.add(option2);
            });
            
            // تحديد العملة الافتراضية
            symbolSelect.value = data.default;
            this.currentSymbol = data.default;
            
        } catch (error) {
            console.error('خطأ في تحميل قائمة العملات:', error);
            this.showError('فشل في تحميل قائمة العملات');
        }
    }
    
    setupEventListeners() {
        // تغيير العملة
        document.getElementById('symbolSelect').addEventListener('change', (e) => {
            if (e.target.value) {
                this.currentSymbol = e.target.value;
                this.loadData();
            }
        });
        
        // زر التحديث
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadData();
        });
        
        // زر التدريب
        document.getElementById('trainBtn').addEventListener('click', () => {
            this.trainModel();
        });
    }
    
    async loadInitialData() {
        await this.loadData();
        await this.loadSystemStatus();
    }
    
    async loadData() {
        if (!this.currentSymbol) return;
        
        try {
            this.showLoading();
            
            // تحميل البيانات والتنبؤات بشكل متوازي
            const [dataResponse, predictionsResponse] = await Promise.all([
                fetch(`/api/data/${this.currentSymbol}`),
                fetch(`/api/predict/${this.currentSymbol}`)
            ]);
            
            if (dataResponse.ok) {
                const data = await dataResponse.json();
                this.updatePriceCards(data);
                this.updateCharts(data);
                this.updateSentiment(data.sentiment_score);
            }
            
            if (predictionsResponse.ok) {
                const predictions = await predictionsResponse.json();
                this.updatePredictions(predictions);
            }
            
            this.updateLastUpdateTime();
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            this.showError('فشل في تحميل البيانات');
        } finally {
            this.hideLoading();
        }
    }
    
    updatePriceCards(data) {
        const priceCardsContainer = document.getElementById('priceCards');
        
        if (!data.real_time_data || !data.real_time_data.current_price) {
            priceCardsContainer.innerHTML = '<div class="col-12 text-center text-muted">لا توجد بيانات متاحة</div>';
            return;
        }
        
        const realTimeData = data.real_time_data;
        const currentPrice = realTimeData.current_price;
        const previousClose = realTimeData.previous_close || currentPrice;
        const change = currentPrice - previousClose;
        const changePercent = ((change / previousClose) * 100).toFixed(2);
        
        const isPositive = change >= 0;
        const cardClass = isPositive ? 'positive' : 'negative';
        const changeIcon = isPositive ? 'fa-arrow-up' : 'fa-arrow-down';
        const changeSymbol = isPositive ? '+' : '';
        
        priceCardsContainer.innerHTML = `
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="price-card ${cardClass}">
                    <div class="price-symbol">${this.getSymbolName(this.currentSymbol)}</div>
                    <div class="price-value ltr-numbers">$${currentPrice.toLocaleString()}</div>
                    <div class="price-change">
                        <i class="fas ${changeIcon}"></i>
                        <span class="ltr-numbers">${changeSymbol}${change.toFixed(2)} (${changeSymbol}${changePercent}%)</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">أعلى سعر اليوم</h6>
                        <h4 class="text-success ltr-numbers">$${(realTimeData.day_high || currentPrice).toLocaleString()}</h4>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">أقل سعر اليوم</h6>
                        <h4 class="text-danger ltr-numbers">$${(realTimeData.day_low || currentPrice).toLocaleString()}</h4>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">حجم التداول</h6>
                        <h4 class="text-info ltr-numbers">${this.formatVolume(realTimeData.volume || 0)}</h4>
                    </div>
                </div>
            </div>
        `;
    }
    
    updateCharts(data) {
        if (!data.historical_data || data.historical_data.length === 0) {
            return;
        }
        
        // تحديث الرسم البياني للأسعار
        this.updatePriceChart(data.historical_data);
        
        // تحديث رسم المؤشرات الفنية
        this.updateIndicatorsChart(data.historical_data);
    }
    
    updatePriceChart(historicalData) {
        const ctx = document.getElementById('priceChart').getContext('2d');
        
        // تحضير البيانات
        const labels = historicalData.slice(-30).map(item => {
            const date = new Date(item.date || item.Date);
            return date.toLocaleDateString('ar-SA');
        });
        
        const prices = historicalData.slice(-30).map(item => item.close || item.Close);
        
        // إزالة الرسم السابق إن وجد
        if (this.charts.priceChart) {
            this.charts.priceChart.destroy();
        }
        
        // إنشاء الرسم الجديد
        this.charts.priceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'السعر ($)',
                    data: prices,
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }
    
    updateIndicatorsChart(historicalData) {
        const ctx = document.getElementById('indicatorsChart').getContext('2d');
        
        // تحضير بيانات المؤشرات
        const labels = historicalData.slice(-20).map(item => {
            const date = new Date(item.date || item.Date);
            return date.toLocaleDateString('ar-SA');
        });
        
        const rsi = historicalData.slice(-20).map(item => item.rsi_14 || 50);
        const macd = historicalData.slice(-20).map(item => item.macd || 0);
        
        // إزالة الرسم السابق إن وجد
        if (this.charts.indicatorsChart) {
            this.charts.indicatorsChart.destroy();
        }
        
        // إنشاء الرسم الجديد
        this.charts.indicatorsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'RSI',
                        data: rsi,
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        yAxisID: 'y'
                    },
                    {
                        label: 'MACD',
                        data: macd,
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        borderWidth: 2,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        min: 0,
                        max: 100,
                        title: {
                            display: true,
                            text: 'RSI'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'MACD'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    }
    
    updatePredictions(predictions) {
        const predictionsPanel = document.getElementById('predictionsPanel');
        
        if (!predictions || !predictions.predictions) {
            predictionsPanel.innerHTML = '<div class="text-center text-muted">لا توجد تنبؤات متاحة</div>';
            return;
        }
        
        let html = '';
        
        // تنبؤات LSTM
        if (predictions.predictions.lstm) {
            html += '<h6 class="mb-3"><i class="fas fa-brain me-2"></i>تنبؤات LSTM</h6>';
            
            Object.entries(predictions.predictions.lstm).forEach(([horizon, predData]) => {
                const changeClass = predData.change_percentage >= 0 ? 'positive' : 'negative';
                const changeIcon = predData.change_percentage >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                
                html += `
                    <div class="prediction-item">
                        <div class="prediction-horizon">${horizon} ${horizon === '1d' ? 'يوم' : 'أيام'}</div>
                        <div class="prediction-price ltr-numbers">${predData.formatted_price}</div>
                        <div class="prediction-change ${changeClass}">
                            <i class="fas ${changeIcon}"></i>
                            <span class="ltr-numbers">${predData.change_percentage.toFixed(2)}%</span>
                            <span>${predData.emoji}</span>
                        </div>
                    </div>
                `;
            });
        }
        
        // تنبؤات Ensemble
        if (predictions.predictions.ensemble) {
            html += '<h6 class="mb-3 mt-4"><i class="fas fa-layer-group me-2"></i>تنبؤات متقدمة</h6>';
            
            Object.entries(predictions.predictions.ensemble).forEach(([horizon, predData]) => {
                const changeClass = predData.change_percentage >= 0 ? 'positive' : 'negative';
                const changeIcon = predData.change_percentage >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                
                html += `
                    <div class="prediction-item">
                        <div class="prediction-horizon">${horizon} ${horizon === '1d' ? 'يوم' : 'أيام'}</div>
                        <div class="prediction-price ltr-numbers">${predData.formatted_price}</div>
                        <div class="prediction-change ${changeClass}">
                            <i class="fas ${changeIcon}"></i>
                            <span class="ltr-numbers">${predData.change_percentage.toFixed(2)}%</span>
                            <span>${predData.emoji}</span>
                        </div>
                    </div>
                `;
            });
        }
        
        if (html === '') {
            html = '<div class="text-center text-muted">لا توجد تنبؤات متاحة</div>';
        }
        
        predictionsPanel.innerHTML = html;
    }
    
    updateSentiment(sentimentScore) {
        const sentimentValue = document.getElementById('sentimentValue');
        
        if (sentimentScore !== undefined && sentimentScore !== null) {
            sentimentValue.textContent = sentimentScore.toFixed(2);
            
            // تحديث لون المقياس حسب القيمة
            const meter = sentimentValue.closest('.sentiment-meter');
            if (sentimentScore > 0.1) {
                meter.style.borderColor = '#10b981';
            } else if (sentimentScore < -0.1) {
                meter.style.borderColor = '#ef4444';
            } else {
                meter.style.borderColor = '#f59e0b';
            }
        } else {
            sentimentValue.textContent = '0.00';
        }
    }
    
    async loadSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            this.updateModelStatus(status.model_status);
            
        } catch (error) {
            console.error('خطأ في تحميل حالة النظام:', error);
        }
    }
    
    updateModelStatus(modelStatus) {
        const modelStatusContainer = document.getElementById('modelStatus');
        
        let html = '';
        
        Object.entries(modelStatus).forEach(([symbol, status]) => {
            const lstmBadge = status.lstm_trained ? 
                '<span class="model-status-badge trained">مدرب</span>' : 
                '<span class="model-status-badge not-trained">غير مدرب</span>';
            
            const ensembleBadge = status.ensemble_trained ? 
                '<span class="model-status-badge trained">مدرب</span>' : 
                '<span class="model-status-badge not-trained">غير مدرب</span>';
            
            html += `
                <div class="model-status-item">
                    <div>
                        <strong>${this.getSymbolName(symbol)}</strong>
                        <div class="small text-muted">
                            LSTM: ${lstmBadge} | Ensemble: ${ensembleBadge}
                        </div>
                    </div>
                </div>
            `;
        });
        
        modelStatusContainer.innerHTML = html;
    }
    
    async trainModel() {
        const trainSymbolSelect = document.getElementById('trainSymbolSelect');
        const selectedSymbol = trainSymbolSelect.value;
        
        if (!selectedSymbol) {
            this.showError('يرجى اختيار عملة للتدريب');
            return;
        }
        
        if (this.isTraining) {
            this.showError('التدريب قيد التنفيذ بالفعل');
            return;
        }
        
        try {
            this.isTraining = true;
            this.showTrainingProgress();
            
            const response = await fetch(`/api/train/${selectedSymbol}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showSuccess(`تم تدريب النموذج بنجاح لـ ${this.getSymbolName(selectedSymbol)}`);
                await this.loadSystemStatus(); // تحديث حالة النماذج
            } else {
                this.showError(result.error || 'فشل في التدريب');
            }
            
        } catch (error) {
            console.error('خطأ في التدريب:', error);
            this.showError('خطأ في الاتصال بالخادم');
        } finally {
            this.isTraining = false;
            this.hideTrainingProgress();
        }
    }
    
    showTrainingProgress() {
        const progressContainer = document.getElementById('trainingProgress');
        const trainBtn = document.getElementById('trainBtn');
        
        progressContainer.classList.remove('d-none');
        trainBtn.disabled = true;
        trainBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التدريب...';
    }
    
    hideTrainingProgress() {
        const progressContainer = document.getElementById('trainingProgress');
        const trainBtn = document.getElementById('trainBtn');
        
        progressContainer.classList.add('d-none');
        trainBtn.disabled = false;
        trainBtn.innerHTML = '<i class="fas fa-play me-1"></i>بدء التدريب';
    }
    
    startAutoUpdate() {
        // تحديث كل 5 دقائق
        this.updateInterval = setInterval(() => {
            this.loadData();
        }, 5 * 60 * 1000);
    }
    
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    updateLastUpdateTime() {
        const lastUpdateElement = document.getElementById('lastUpdate');
        const now = new Date();
        lastUpdateElement.textContent = now.toLocaleString('ar-SA');
    }
    
    showLoading() {
        // يمكن إضافة مؤشر تحميل هنا
    }
    
    hideLoading() {
        // إخفاء مؤشر التحميل
    }
    
    showError(message) {
        // عرض رسالة خطأ
        console.error(message);
        // يمكن إضافة toast notification هنا
    }
    
    showSuccess(message) {
        // عرض رسالة نجاح
        console.log(message);
        // يمكن إضافة toast notification هنا
    }
    
    getSymbolName(symbol) {
        const symbolNames = {
            'BTC-USD': 'بيتكوين',
            'ETH-USD': 'إيثيريوم',
            'ADA-USD': 'كاردانو',
            'DOT-USD': 'بولكادوت',
            'LINK-USD': 'تشين لينك'
        };
        
        return symbolNames[symbol] || symbol;
    }
    
    formatVolume(volume) {
        if (volume >= 1e9) {
            return (volume / 1e9).toFixed(2) + 'B';
        } else if (volume >= 1e6) {
            return (volume / 1e6).toFixed(2) + 'M';
        } else if (volume >= 1e3) {
            return (volume / 1e3).toFixed(2) + 'K';
        }
        return volume.toLocaleString();
    }
}

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new CryptoDashboard();
});
