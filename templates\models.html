<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النماذج - نظام التنبؤ بالعملات المشفرة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                نظام التنبؤ بالعملات المشفرة
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('predictions') }}">
                            <i class="fas fa-crystal-ball me-1"></i>التنبؤات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('analysis') }}">
                            <i class="fas fa-chart-bar me-1"></i>التحليل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('models_page') }}">
                            <i class="fas fa-cogs me-1"></i>النماذج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            إدارة نماذج الذكاء الاصطناعي
                        </h4>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Status Overview -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="h2 text-primary mb-2" id="totalModels">0</div>
                        <h6 class="card-title">إجمالي النماذج</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="h2 text-success mb-2" id="trainedModels">0</div>
                        <h6 class="card-title">النماذج المدربة</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="h2 text-warning mb-2" id="trainingModels">0</div>
                        <h6 class="card-title">قيد التدريب</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <div class="h2 text-info mb-2" id="avgAccuracy">0%</div>
                        <h6 class="card-title">متوسط الدقة</h6>
                    </div>
                </div>
            </div>
        </div>

        <!-- Training Section -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-play me-2"></i>
                            تدريب نموذج جديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="trainingForm">
                            <div class="mb-3">
                                <label for="trainSymbol" class="form-label">اختر العملة</label>
                                <select class="form-select" id="trainSymbol" required>
                                    <option value="">اختر العملة للتدريب...</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="modelType" class="form-label">نوع النموذج</label>
                                <select class="form-select" id="modelType" required>
                                    <option value="lstm">LSTM Neural Network</option>
                                    <option value="ensemble">Ensemble Model</option>
                                    <option value="both">كلا النموذجين</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="epochs" class="form-label">عدد العصور (Epochs)</label>
                                <input type="number" class="form-control" id="epochs" value="50" min="10" max="200">
                            </div>
                            <div class="mb-3">
                                <label for="batchSize" class="form-label">حجم الدفعة (Batch Size)</label>
                                <input type="number" class="form-control" id="batchSize" value="32" min="16" max="128">
                            </div>
                            <button type="submit" class="btn btn-primary w-100" id="startTrainingBtn">
                                <i class="fas fa-play me-2"></i>
                                بدء التدريب
                            </button>
                        </form>
                        
                        <!-- Training Progress -->
                        <div id="trainingProgress" class="d-none mt-4">
                            <div class="d-flex justify-content-between mb-2">
                                <span>التقدم</span>
                                <span id="progressPercent">0%</span>
                            </div>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" style="width: 0%"></div>
                            </div>
                            <div class="small text-muted" id="trainingStatus">جاري التدريب...</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            أداء التدريب
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="trainingChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Models List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                قائمة النماذج
                            </h5>
                            <button class="btn btn-outline-primary btn-sm" id="refreshModelsBtn">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-custom" id="modelsTable">
                                <thead>
                                    <tr>
                                        <th>العملة</th>
                                        <th>نوع النموذج</th>
                                        <th>الحالة</th>
                                        <th>الدقة</th>
                                        <th>تاريخ التدريب</th>
                                        <th>حجم النموذج</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Models data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model Details Modal -->
        <div class="modal fade" id="modelDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل النموذج</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="modelDetailsContent">
                        <!-- Model details will be loaded here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-danger" id="deleteModelBtn">حذف النموذج</button>
                        <button type="button" class="btn btn-primary" id="retrainModelBtn">إعادة التدريب</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Training Log Modal -->
        <div class="modal fade" id="trainingLogModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">سجل التدريب</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <h6>منحنى الخسارة</h6>
                                <canvas id="lossChart" height="300"></canvas>
                            </div>
                            <div class="col-lg-6">
                                <h6>منحنى الدقة</h6>
                                <canvas id="accuracyChart" height="300"></canvas>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h6>سجل التدريب المفصل</h6>
                            <div class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto;" id="trainingLog">
                                <!-- Training log will be displayed here -->
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" id="downloadLogBtn">
                            <i class="fas fa-download me-1"></i>
                            تحميل السجل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Shared Navigation JS -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/models.js') }}"></script>
</body>
</html>
