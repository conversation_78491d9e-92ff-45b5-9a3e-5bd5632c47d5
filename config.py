"""
إعدادات النظام المتقدم للتنبؤ بأسعار العملات المشفرة
Advanced Cryptocurrency Price Forecasting System Configuration
"""

import os
from datetime import datetime, timedelta

class Config:
    """إعدادات النظام الرئيسية"""
    
    # إعدادات البيانات - Data Settings
    CRYPTO_SYMBOLS = ['BTC-USD', 'ETH-USD', 'ADA-USD', 'DOT-USD', 'LINK-USD']
    DEFAULT_SYMBOL = 'BTC-USD'
    
    # فترة البيانات التاريخية - Historical Data Period
    DATA_PERIOD = '2y'  # سنتان من البيانات
    DATA_INTERVAL = '1d'  # بيانات يومية
    
    # إعدادات النافذة الزمنية - Time Window Settings
    SEQUENCE_LENGTH = 60  # 60 يوم للتنبؤ
    PREDICTION_HORIZONS = [1, 3, 7, 30]  # آفاق التنبؤ بالأيام
    
    # إعدادات النموذج - Model Settings
    LSTM_UNITS_1 = 64
    LSTM_UNITS_2 = 32
    DROPOUT_RATE = 0.2
    BATCH_SIZE = 32
    EPOCHS = 50
    VALIDATION_SPLIT = 0.1
    LEARNING_RATE = 0.001
    
    # المؤشرات الفنية - Technical Indicators
    TECHNICAL_INDICATORS = {
        'RSI': {'period': 14},
        'MACD': {'fast': 12, 'slow': 26, 'signal': 9},
        'SMA': {'periods': [10, 20, 50]},
        'EMA': {'periods': [10, 20, 50]},
        'BOLLINGER': {'period': 20, 'std': 2},
        'STOCH': {'k_period': 14, 'd_period': 3},
        'ATR': {'period': 14},
        'OBV': {},
        'VWAP': {}
    }
    
    # إعدادات تحليل المشاعر - Sentiment Analysis Settings
    SENTIMENT_MODEL = 'ProsusAI/finbert'
    SENTIMENT_SOURCES = ['twitter', 'reddit', 'news']
    SENTIMENT_KEYWORDS = ['bitcoin', 'btc', 'cryptocurrency', 'crypto']
    
    # إعدادات الخادم - Server Settings
    HOST = '0.0.0.0'
    PORT = 5000
    DEBUG = True
    
    # مسارات الملفات - File Paths
    DATA_DIR = 'data'
    MODELS_DIR = 'saved_models'
    LOGS_DIR = 'logs'
    
    # إعدادات قاعدة البيانات - Database Settings (اختياري)
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///crypto_predictions.db')
    
    # مفاتيح API - API Keys
    TWITTER_API_KEY = os.getenv('TWITTER_API_KEY')
    TWITTER_API_SECRET = os.getenv('TWITTER_API_SECRET')
    NEWS_API_KEY = os.getenv('NEWS_API_KEY')
    
    # إعدادات التحديث - Update Settings
    UPDATE_INTERVAL_MINUTES = 60  # تحديث كل ساعة
    RETRAIN_INTERVAL_DAYS = 7     # إعادة تدريب كل أسبوع
    
    # إعدادات الأمان - Security Settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    
    @classmethod
    def create_directories(cls):
        """إنشاء المجلدات المطلوبة"""
        directories = [cls.DATA_DIR, cls.MODELS_DIR, cls.LOGS_DIR]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def get_model_path(cls, symbol, model_type='lstm'):
        """الحصول على مسار النموذج"""
        return os.path.join(cls.MODELS_DIR, f'{symbol}_{model_type}_model.h5')
    
    @classmethod
    def get_data_path(cls, symbol):
        """الحصول على مسار البيانات"""
        return os.path.join(cls.DATA_DIR, f'{symbol}_data.csv')

class ModelConfig:
    """إعدادات النماذج المتقدمة"""
    
    # إعدادات LSTM
    LSTM_CONFIG = {
        'layers': [
            {'units': 64, 'return_sequences': True, 'dropout': 0.2},
            {'units': 32, 'return_sequences': False, 'dropout': 0.2}
        ],
        'dense_layers': [
            {'units': 25, 'activation': 'relu'},
            {'units': 1, 'activation': 'linear'}
        ],
        'optimizer': 'adam',
        'loss': 'mse',
        'metrics': ['mae']
    }
    
    # إعدادات XGBoost
    XGBOOST_CONFIG = {
        'n_estimators': 100,
        'max_depth': 6,
        'learning_rate': 0.1,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'random_state': 42
    }
    
    # إعدادات Random Forest
    RF_CONFIG = {
        'n_estimators': 100,
        'max_depth': 10,
        'random_state': 42,
        'n_jobs': -1
    }
    
    # إعدادات ARIMA
    ARIMA_CONFIG = {
        'order': (5, 1, 0),
        'seasonal_order': (1, 1, 1, 12)
    }

# إنشاء المجلدات عند استيراد الملف
Config.create_directories()
