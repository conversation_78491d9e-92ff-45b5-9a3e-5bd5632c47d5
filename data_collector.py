"""
وحدة جمع البيانات للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Data Collection Module for Advanced Cryptocurrency Price Forecasting System
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
import logging
from config import Config
from utils import logger, clean_data, validate_data

class CryptoDataCollector:
    """جامع البيانات الرئيسي للعملات المشفرة"""
    
    def __init__(self):
        self.logger = logger
        self.sentiment_analyzer = None
        self._initialize_sentiment_analyzer()
    
    def _initialize_sentiment_analyzer(self):
        """تهيئة محلل المشاعر"""
        try:
            self.sentiment_analyzer = pipeline(
                "sentiment-analysis",
                model=Config.SENTIMENT_MODEL,
                tokenizer=Config.SENTIMENT_MODEL
            )
            self.logger.info("تم تهيئة محلل المشاعر بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة محلل المشاعر: {e}")
            self.sentiment_analyzer = None
    
    def get_historical_data(self, symbol, period=None, interval=None):
        """جمع البيانات التاريخية"""
        if period is None:
            period = Config.DATA_PERIOD
        if interval is None:
            interval = Config.DATA_INTERVAL
        
        try:
            self.logger.info(f"جمع البيانات التاريخية لـ {symbol}")
            
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                raise ValueError(f"لا توجد بيانات متاحة لـ {symbol}")
            
            # تنظيف أسماء الأعمدة
            data.columns = [col.lower().replace(' ', '_') for col in data.columns]
            
            # إضافة معلومات إضافية
            data['symbol'] = symbol
            data['date'] = data.index
            
            self.logger.info(f"تم جمع {len(data)} سجل لـ {symbol}")
            return data
            
        except Exception as e:
            self.logger.error(f"خطأ في جمع البيانات لـ {symbol}: {e}")
            return pd.DataFrame()
    
    def get_real_time_data(self, symbol):
        """جمع البيانات في الوقت الفعلي"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            current_data = {
                'symbol': symbol,
                'current_price': info.get('currentPrice', 0),
                'previous_close': info.get('previousClose', 0),
                'open': info.get('open', 0),
                'day_high': info.get('dayHigh', 0),
                'day_low': info.get('dayLow', 0),
                'volume': info.get('volume', 0),
                'market_cap': info.get('marketCap', 0),
                'timestamp': datetime.now()
            }
            
            return current_data
            
        except Exception as e:
            self.logger.error(f"خطأ في جمع البيانات الفورية لـ {symbol}: {e}")
            return {}
    
    def get_crypto_news(self, symbol, limit=10):
        """جمع الأخبار المتعلقة بالعملة المشفرة"""
        try:
            # استخدام NewsAPI أو مصادر أخرى
            crypto_name = symbol.replace('-USD', '').lower()
            
            # مثال على جمع الأخبار (يحتاج إلى API key)
            news_data = []
            
            # يمكن إضافة تكامل مع NewsAPI هنا
            # if Config.NEWS_API_KEY:
            #     news_data = self._fetch_news_from_api(crypto_name, limit)
            
            return news_data
            
        except Exception as e:
            self.logger.error(f"خطأ في جمع الأخبار لـ {symbol}: {e}")
            return []
    
    def analyze_sentiment(self, texts):
        """تحليل المشاعر للنصوص"""
        if not self.sentiment_analyzer or not texts:
            return []
        
        try:
            sentiments = []
            
            for text in texts:
                if len(text) > 512:  # قطع النص إذا كان طويلاً
                    text = text[:512]
                
                result = self.sentiment_analyzer(text)
                
                # تحويل النتيجة إلى درجة رقمية
                if result[0]['label'] == 'POSITIVE':
                    score = result[0]['score']
                elif result[0]['label'] == 'NEGATIVE':
                    score = -result[0]['score']
                else:
                    score = 0
                
                sentiments.append({
                    'text': text[:100] + '...' if len(text) > 100 else text,
                    'sentiment': result[0]['label'],
                    'score': score,
                    'confidence': result[0]['score']
                })
            
            return sentiments
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل المشاعر: {e}")
            return []
    
    def get_sentiment_score(self, symbol, days=7):
        """الحصول على درجة المشاعر الإجمالية"""
        try:
            # جمع الأخبار والتغريدات
            news = self.get_crypto_news(symbol, limit=20)
            
            if not news:
                return 0.0  # محايد إذا لم توجد بيانات
            
            # استخراج النصوص
            texts = [item.get('title', '') + ' ' + item.get('description', '') 
                    for item in news if item.get('title')]
            
            if not texts:
                return 0.0
            
            # تحليل المشاعر
            sentiments = self.analyze_sentiment(texts)
            
            if not sentiments:
                return 0.0
            
            # حساب المتوسط المرجح
            total_score = sum(s['score'] * s['confidence'] for s in sentiments)
            total_weight = sum(s['confidence'] for s in sentiments)
            
            if total_weight == 0:
                return 0.0
            
            average_sentiment = total_score / total_weight
            
            self.logger.info(f"درجة المشاعر لـ {symbol}: {average_sentiment:.3f}")
            return average_sentiment
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب درجة المشاعر لـ {symbol}: {e}")
            return 0.0
    
    def get_market_data(self, symbols=None):
        """جمع بيانات السوق الشاملة"""
        if symbols is None:
            symbols = Config.CRYPTO_SYMBOLS
        
        market_data = {}
        
        for symbol in symbols:
            try:
                # البيانات التاريخية
                historical = self.get_historical_data(symbol)
                
                # البيانات الفورية
                real_time = self.get_real_time_data(symbol)
                
                # درجة المشاعر
                sentiment = self.get_sentiment_score(symbol)
                
                market_data[symbol] = {
                    'historical_data': historical,
                    'real_time_data': real_time,
                    'sentiment_score': sentiment,
                    'last_updated': datetime.now()
                }
                
                self.logger.info(f"تم جمع البيانات الشاملة لـ {symbol}")
                
            except Exception as e:
                self.logger.error(f"خطأ في جمع البيانات الشاملة لـ {symbol}: {e}")
                market_data[symbol] = None
        
        return market_data
    
    def save_data(self, data, symbol):
        """حفظ البيانات في ملف"""
        try:
            filepath = Config.get_data_path(symbol)
            data.to_csv(filepath, index=True)
            self.logger.info(f"تم حفظ البيانات في: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"خطأ في حفظ البيانات: {e}")
            return False
    
    def load_data(self, symbol):
        """تحميل البيانات من ملف"""
        try:
            filepath = Config.get_data_path(symbol)
            data = pd.read_csv(filepath, index_col=0, parse_dates=True)
            self.logger.info(f"تم تحميل البيانات من: {filepath}")
            return data
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات: {e}")
            return pd.DataFrame()

# مثال على الاستخدام
if __name__ == "__main__":
    collector = CryptoDataCollector()
    
    # جمع البيانات لعملة البيتكوين
    btc_data = collector.get_historical_data('BTC-USD')
    print(f"تم جمع {len(btc_data)} سجل لعملة البيتكوين")
    
    # الحصول على درجة المشاعر
    sentiment = collector.get_sentiment_score('BTC-USD')
    print(f"درجة المشاعر: {sentiment}")
    
    # حفظ البيانات
    if not btc_data.empty:
        collector.save_data(btc_data, 'BTC-USD')
