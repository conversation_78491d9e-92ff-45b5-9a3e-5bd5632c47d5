#!/usr/bin/env python3
"""
ملف تجريبي للنظام المتقدم للتنبؤ بأسعار العملات المشفرة
Demo Script for Advanced Cryptocurrency Price Forecasting System
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from data_collector import CryptoDataCollector
from feature_engineering import FeatureEngineer
from models.lstm_model import LSTMPredictor
from models.ensemble_model import EnsemblePredictor
from utils import logger, calculate_metrics, format_currency, get_trend_direction
from config import Config

def print_section(title):
    """طباعة عنوان القسم"""
    print("\n" + "="*60)
    print(f"🔹 {title}")
    print("="*60)

def demo_data_collection():
    """عرض توضيحي لجمع البيانات"""
    print_section("جمع البيانات والمؤشرات الفنية")
    
    # إنشاء جامع البيانات
    collector = CryptoDataCollector()
    
    # جمع البيانات لعملة البيتكوين
    print("📊 جمع البيانات التاريخية لعملة البيتكوين...")
    btc_data = collector.get_historical_data('BTC-USD', period='6mo')
    
    if btc_data.empty:
        print("❌ فشل في جمع البيانات")
        return None
    
    print(f"✅ تم جمع {len(btc_data)} سجل")
    print(f"📅 من {btc_data.index[0].date()} إلى {btc_data.index[-1].date()}")
    
    # عرض آخر 5 سجلات
    print("\n📈 آخر 5 أسعار:")
    print(btc_data[['open', 'high', 'low', 'close', 'volume']].tail().round(2))
    
    # الحصول على البيانات الفورية
    print("\n⚡ البيانات الفورية:")
    real_time = collector.get_real_time_data('BTC-USD')
    if real_time:
        current_price = real_time.get('current_price', 0)
        previous_close = real_time.get('previous_close', 0)
        change = current_price - previous_close
        change_pct = (change / previous_close * 100) if previous_close > 0 else 0
        
        print(f"💰 السعر الحالي: {format_currency(current_price)}")
        print(f"📊 التغيير: {change:+.2f} ({change_pct:+.2f}%)")
        print(f"📈 أعلى سعر اليوم: {format_currency(real_time.get('day_high', 0))}")
        print(f"📉 أقل سعر اليوم: {format_currency(real_time.get('day_low', 0))}")
    
    # تحليل المشاعر
    print("\n💭 تحليل المشاعر:")
    sentiment = collector.get_sentiment_score('BTC-USD')
    print(f"🎭 درجة المشاعر: {sentiment:.3f}")
    
    if sentiment > 0.1:
        print("😊 المشاعر إيجابية")
    elif sentiment < -0.1:
        print("😟 المشاعر سلبية")
    else:
        print("😐 المشاعر محايدة")
    
    return btc_data

def demo_feature_engineering(data):
    """عرض توضيحي لهندسة الخصائص"""
    print_section("هندسة الخصائص والمؤشرات الفنية")
    
    if data is None or data.empty:
        print("❌ لا توجد بيانات للمعالجة")
        return None
    
    # إنشاء مهندس الخصائص
    engineer = FeatureEngineer()
    
    print("🔧 إنشاء الخصائص والمؤشرات الفنية...")
    featured_data = engineer.create_features(data)
    
    print(f"✅ تم إنشاء {len(engineer.feature_columns)} خاصية")
    print(f"📊 عدد السجلات بعد المعالجة: {len(featured_data)}")
    
    # عرض بعض المؤشرات الفنية
    print("\n📈 المؤشرات الفنية الحديثة:")
    latest = featured_data.iloc[-1]
    
    indicators = {
        'RSI (14)': latest.get('rsi_14', 0),
        'MACD': latest.get('macd', 0),
        'SMA (20)': latest.get('sma_20', 0),
        'EMA (20)': latest.get('ema_20', 0),
        'Bollinger Upper': latest.get('bb_upper', 0),
        'Bollinger Lower': latest.get('bb_lower', 0),
        'Volume SMA (20)': latest.get('volume_sma_20', 0)
    }
    
    for name, value in indicators.items():
        if value != 0:
            print(f"📊 {name}: {value:.2f}")
    
    # حساب أهمية الخصائص
    print("\n🎯 أهمية الخصائص:")
    try:
        importance = engineer.get_feature_importance(featured_data)
        if not importance.empty:
            print("أهم 10 خصائص:")
            for i, (feature, scores) in enumerate(importance.head(10).iterrows(), 1):
                print(f"{i:2d}. {feature}: {scores['combined']:.3f}")
    except Exception as e:
        print(f"⚠️  لا يمكن حساب أهمية الخصائص: {e}")
    
    return featured_data

def demo_lstm_model(data):
    """عرض توضيحي لنموذج LSTM"""
    print_section("نموذج LSTM للتنبؤ")
    
    if data is None or data.empty:
        print("❌ لا توجد بيانات للتدريب")
        return None
    
    # إنشاء نموذج LSTM
    model = LSTMPredictor()
    model.n_features = len(data.columns)
    
    print("🧠 بناء نموذج LSTM...")
    lstm_model = model.build_model('standard')
    
    if lstm_model is None:
        print("❌ فشل في بناء النموذج")
        return None
    
    print("✅ تم بناء النموذج بنجاح")
    print(f"📊 عدد الخصائص: {model.n_features}")
    print(f"🔢 طول التسلسل: {model.sequence_length}")
    
    # تحضير البيانات
    print("\n📋 تحضير البيانات للتدريب...")
    X, y = model.prepare_data(data)
    
    if X is None or len(X) == 0:
        print("❌ فشل في تحضير البيانات")
        return None
    
    print(f"✅ شكل البيانات: X={X.shape}, y={y.shape}")
    
    # تقسيم البيانات
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"🎯 بيانات التدريب: {len(X_train)} عينة")
    print(f"🧪 بيانات الاختبار: {len(X_test)} عينة")
    
    # تدريب النموذج (عدد قليل من العصور للعرض التوضيحي)
    print("\n🚀 بدء التدريب (عرض توضيحي سريع)...")
    
    # تقليل عدد العصور للعرض التوضيحي
    original_epochs = Config.EPOCHS
    Config.EPOCHS = 5
    
    try:
        history = model.train(X_train, y_train, X_test, y_test)
        
        if history is not None:
            print("✅ تم التدريب بنجاح")
            
            # تقييم النموذج
            print("\n📊 تقييم الأداء:")
            metrics = model.evaluate(X_test, y_test)
            
            if metrics:
                for metric, value in metrics.items():
                    print(f"📈 {metric}: {value:.4f}")
            
            # التنبؤ بالمستقبل
            print("\n🔮 التنبؤ بالأسعار المستقبلية:")
            last_sequence = X[-1]
            future_predictions = model.predict_future(last_sequence, steps=7)
            
            if future_predictions is not None:
                current_price = data['close'].iloc[-1]
                
                print(f"💰 السعر الحالي: {format_currency(current_price)}")
                print("\n📅 التنبؤات للأسبوع القادم:")
                
                for i, pred_price in enumerate(future_predictions, 1):
                    trend, emoji = get_trend_direction(current_price, pred_price)
                    change_pct = ((pred_price - current_price) / current_price) * 100
                    
                    print(f"يوم {i}: {format_currency(pred_price)} ({change_pct:+.2f}%) {emoji}")
        
    except Exception as e:
        print(f"❌ خطأ في التدريب: {e}")
    finally:
        # استعادة الإعدادات الأصلية
        Config.EPOCHS = original_epochs
    
    return model

def demo_ensemble_model(data):
    """عرض توضيحي لنموذج التجميع"""
    print_section("نموذج التجميع المتقدم")
    
    if data is None or data.empty or len(data) < 200:
        print("❌ البيانات غير كافية لنموذج التجميع (يحتاج 200+ سجل)")
        return None
    
    print("🎯 إنشاء نموذج التجميع...")
    ensemble = EnsemblePredictor()
    
    print("📊 تدريب النماذج المختلفة...")
    print("⚠️  هذا قد يستغرق بضع دقائق...")
    
    try:
        # تدريب النموذج مع بيانات أقل للعرض التوضيحي
        sample_data = data.tail(500)  # استخدام آخر 500 سجل فقط
        
        results = ensemble.train(sample_data, test_size=0.2, sequence_length=30)
        
        if results:
            print("✅ تم تدريب نموذج التجميع بنجاح")
            
            print("\n📊 نتائج التقييم:")
            for model_name, metrics in results.items():
                if isinstance(metrics, dict) and 'RMSE' in metrics:
                    print(f"\n🔹 {model_name}:")
                    print(f"   RMSE: {metrics['RMSE']:.2f}")
                    print(f"   MAE: {metrics['MAE']:.2f}")
                    print(f"   R²: {metrics['R2']:.3f}")
            
            # التنبؤ باستخدام النموذج المجمع
            print("\n🔮 التنبؤ باستخدام النموذج المجمع:")
            ensemble_pred = ensemble.predict(sample_data.tail(100), method='weighted_average')
            
            if ensemble_pred is not None and len(ensemble_pred) > 0:
                current_price = data['close'].iloc[-1]
                predicted_price = ensemble_pred[-1]
                
                trend, emoji = get_trend_direction(current_price, predicted_price)
                change_pct = ((predicted_price - current_price) / current_price) * 100
                
                print(f"💰 السعر الحالي: {format_currency(current_price)}")
                print(f"🎯 التنبؤ المجمع: {format_currency(predicted_price)}")
                print(f"📈 التغيير المتوقع: {change_pct:+.2f}% {emoji}")
                print(f"🎭 الاتجاه: {trend}")
        
    except Exception as e:
        print(f"❌ خطأ في نموذج التجميع: {e}")
        print("💡 نصيحة: تأكد من توفر جميع المتطلبات وكفاية البيانات")
    
    return ensemble

def demo_visualization(data):
    """عرض توضيحي للرسوم البيانية"""
    print_section("الرسوم البيانية والتحليل البصري")
    
    if data is None or data.empty:
        print("❌ لا توجد بيانات للرسم")
        return
    
    try:
        import matplotlib.pyplot as plt
        plt.style.use('default')
        
        # إعداد الرسم
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('تحليل بيانات البيتكوين', fontsize=16, fontweight='bold')
        
        # الرسم الأول: السعر والحجم
        ax1 = axes[0, 0]
        recent_data = data.tail(60)
        ax1.plot(recent_data.index, recent_data['close'], linewidth=2, color='#2563eb')
        ax1.set_title('سعر الإغلاق (آخر 60 يوم)')
        ax1.set_ylabel('السعر ($)')
        ax1.grid(True, alpha=0.3)
        
        # الرسم الثاني: المؤشرات الفنية
        ax2 = axes[0, 1]
        if 'rsi_14' in recent_data.columns:
            ax2.plot(recent_data.index, recent_data['rsi_14'], color='#10b981', label='RSI')
            ax2.axhline(y=70, color='r', linestyle='--', alpha=0.7, label='مستوى البيع')
            ax2.axhline(y=30, color='g', linestyle='--', alpha=0.7, label='مستوى الشراء')
            ax2.set_title('مؤشر القوة النسبية (RSI)')
            ax2.set_ylabel('RSI')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        # الرسم الثالث: نطاقات بولينجر
        ax3 = axes[1, 0]
        if all(col in recent_data.columns for col in ['bb_upper', 'bb_lower', 'bb_middle']):
            ax3.plot(recent_data.index, recent_data['close'], color='#2563eb', label='السعر')
            ax3.plot(recent_data.index, recent_data['bb_upper'], color='r', alpha=0.7, label='النطاق العلوي')
            ax3.plot(recent_data.index, recent_data['bb_lower'], color='g', alpha=0.7, label='النطاق السفلي')
            ax3.plot(recent_data.index, recent_data['bb_middle'], color='orange', alpha=0.7, label='المتوسط')
            ax3.fill_between(recent_data.index, recent_data['bb_upper'], recent_data['bb_lower'], alpha=0.1)
            ax3.set_title('نطاقات بولينجر')
            ax3.set_ylabel('السعر ($)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        # الرسم الرابع: الحجم
        ax4 = axes[1, 1]
        ax4.bar(recent_data.index, recent_data['volume'], color='#64748b', alpha=0.7)
        ax4.set_title('حجم التداول')
        ax4.set_ylabel('الحجم')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        print("✅ تم عرض الرسوم البيانية")
        
    except ImportError:
        print("⚠️  matplotlib غير متوفر - تخطي الرسوم البيانية")
    except Exception as e:
        print(f"❌ خطأ في الرسم: {e}")

def main():
    """الدالة الرئيسية للعرض التوضيحي"""
    print("🚀 مرحباً بك في العرض التوضيحي للنظام المتقدم للتنبؤ بأسعار العملات المشفرة")
    print("📝 هذا العرض سيوضح جميع ميزات النظام خطوة بخطوة")
    
    try:
        # 1. جمع البيانات
        data = demo_data_collection()
        
        # 2. هندسة الخصائص
        featured_data = demo_feature_engineering(data)
        
        # 3. نموذج LSTM
        lstm_model = demo_lstm_model(featured_data)
        
        # 4. نموذج التجميع
        ensemble_model = demo_ensemble_model(featured_data)
        
        # 5. الرسوم البيانية
        demo_visualization(featured_data)
        
        print_section("انتهى العرض التوضيحي")
        print("🎉 تم عرض جميع ميزات النظام بنجاح!")
        print("🌐 لتشغيل النظام الكامل: python run_system.py")
        print("📚 للمزيد من المعلومات: راجع ملف README.md")
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف العرض التوضيحي")
    except Exception as e:
        print(f"\n❌ خطأ في العرض التوضيحي: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
